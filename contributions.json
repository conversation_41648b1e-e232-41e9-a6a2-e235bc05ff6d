{"$schema": "./contributions.schema.json", "version": 1, "commands": {"gitlens.addAuthors": {"label": "Add Co-authors...", "icon": "$(person-add)", "commandPalette": "gitlens:enabled && !gitlens:readonly && !gitlens:untrusted && !gitlens:hasVirtualFolders", "menus": {"git.commit": [{"when": "gitlens:enabled && !gitlens:readonly && !gitlens:untrusted && !gitlens:hasVirtualFolders && config.gitlens.menus.scmRepository.authors", "group": "4_git<PERSON>s", "order": 1}], "scm/title": [{"when": "scmProvider == git && gitlens:enabled && !gitlens:readonly && !gitlens:untrusted && !gitlens:hasVirtualFolders && config.gitlens.menus.scmRepository.authors", "group": "2_git<PERSON>s", "order": 1}]}}, "gitlens.ai.explainBranch": {"label": "Explain Branch Changes (Preview)...", "commandPalette": "gitlens:enabled && !gitlens:readonly && !gitlens:untrusted && gitlens:gk:organization:ai:enabled"}, "gitlens.ai.explainBranch:graph": {"label": "Explain Branch Changes (Preview)", "icon": "$(sparkle)", "menus": {"webview/context": [{"when": "webviewItem =~ /gitlens:branch\\b/ && !listMultiSelection && !gitlens:readonly && !gitlens:untrusted && gitlens:gk:organization:ai:enabled", "group": "1_gitlens_actions_4", "order": 100}]}}, "gitlens.ai.explainBranch:views": {"label": "Explain Branch Changes (Preview)", "icon": "$(sparkle)", "menus": {"view/item/context": [{"when": "viewItem =~ /gitlens:branch\\b/ && !listMultiSelection && !gitlens:readonly && !gitlens:untrusted && gitlens:gk:organization:ai:enabled", "group": "3_gitlens_ai", "order": 1}]}}, "gitlens.ai.explainCommit": {"label": "Explain Commit Changes (Preview)...", "commandPalette": "gitlens:enabled && !gitlens:readonly && !gitlens:untrusted && gitlens:gk:organization:ai:enabled"}, "gitlens.ai.explainCommit:graph": {"label": "Explain Changes (Preview)", "icon": "$(sparkle)", "menus": {"webview/context": [{"when": "webviewItem =~ /gitlens:commit\\b/ && !listMultiSelection && !gitlens:readonly && !gitlens:untrusted && gitlens:gk:organization:ai:enabled", "group": "1_gitlens_actions_3", "order": 1}]}}, "gitlens.ai.explainCommit:views": {"label": "Explain Changes (Preview)", "icon": "$(sparkle)", "menus": {"view/item/context": [{"when": "viewItem =~ /gitlens:commit\\b/ && !listMultiSelection && !gitlens:readonly && !gitlens:untrusted && gitlens:gk:organization:ai:enabled", "group": "3_gitlens_explore", "order": 1}]}}, "gitlens.ai.explainStash": {"label": "Explain Stash Changes (Preview)...", "commandPalette": "gitlens:enabled && !gitlens:readonly && !gitlens:untrusted && gitlens:gk:organization:ai:enabled"}, "gitlens.ai.explainStash:graph": {"label": "Explain Changes (Preview)", "icon": "$(sparkle)", "menus": {"webview/context": [{"when": "webviewItem =~ /gitlens:stash\\b/ && !listMultiSelection && !gitlens:readonly && !gitlens:untrusted && gitlens:gk:organization:ai:enabled", "group": "1_gitlens_actions_3", "order": 1}]}}, "gitlens.ai.explainStash:views": {"label": "Explain Changes (Preview)", "icon": "$(sparkle)", "menus": {"view/item/context": [{"when": "viewItem =~ /gitlens:stash\\b/ && !listMultiSelection && !gitlens:readonly && !gitlens:untrusted && gitlens:gk:organization:ai:enabled", "group": "3_gitlens_explore", "order": 1}]}}, "gitlens.ai.explainWip": {"label": "Explain Working Changes (Preview)...", "commandPalette": "gitlens:enabled && !gitlens:readonly && !gitlens:untrusted && gitlens:gk:organization:ai:enabled"}, "gitlens.ai.explainWip:graph": {"label": "Explain Working Changes (Preview)", "icon": "$(sparkle)", "menus": {"webview/context": [{"when": "webviewItem == gitlens:wip && !listMultiSelection && !gitlens:readonly && !gitlens:untrusted && gitlens:gk:organization:ai:enabled", "group": "1_gitlens", "order": 2}]}}, "gitlens.ai.explainWip:views": {"label": "Explain Working Changes (Preview)", "icon": "$(sparkle)", "menus": {"view/item/context": [{"when": "viewItem =~ /gitlens:(worktree\\b(?=.*?\\b\\+working\\b)|uncommitted)\\b/ && !listMultiSelection && !gitlens:readonly && !gitlens:untrusted && gitlens:gk:organization:ai:enabled", "group": "3_gitlens_ai", "order": 1}]}}, "gitlens.ai.generateChangelog": {"label": "Generate Changelog (Preview)...", "commandPalette": "gitlens:enabled && !gitlens:untrusted && gitlens:gk:organization:ai:enabled"}, "gitlens.ai.generateChangelog:views": {"label": "Generate Changelog (Preview)", "icon": "$(sparkle)", "menus": {"view/item/context": [{"when": "viewItem =~ /gitlens:compare:results:commits\\b/ && !gitlens:untrusted && gitlens:gk:organization:ai:enabled && config.gitlens.ai.enabled", "group": "inline", "order": 98}, {"when": "viewItem =~ /gitlens:compare:results:commits\\b/ && !listMultiSelection && !gitlens:untrusted && gitlens:gk:organization:ai:enabled", "group": "4_gitlens_actions", "order": 2}]}}, "gitlens.ai.generateChangelogFrom:graph": {"label": "Generate Changelog (Preview)...", "icon": "$(sparkle)", "menus": {"webview/context": [{"when": "webviewItem =~ /gitlens:(branch|tag)\\b/ && !gitlens:untrusted && gitlens:gk:organization:ai:enabled", "group": "1_gitlens_actions_3", "order": 100}]}}, "gitlens.ai.generateChangelogFrom:views": {"label": "Generate Changelog (Preview)...", "icon": "$(sparkle)", "menus": {"view/item/context": [{"when": "viewItem =~ /gitlens:(branch|tag)\\b/ && !gitlens:untrusted && gitlens:gk:organization:ai:enabled", "group": "1_gitlens_actions_3", "order": 100}]}}, "gitlens.ai.generateCommitMessage": {"label": "Generate Commit Message", "commandPalette": "gitlens:enabled && !gitlens:readonly && !gitlens:untrusted && gitlens:gk:organization:ai:enabled"}, "gitlens.ai.generateCommitMessage:graph": {"label": "Generate Commit Message", "menus": {"webview/context": [{"when": "webviewItem == gitlens:wip && !listMultiSelection && !gitlens:readonly && !gitlens:untrusted && gitlens:gk:organization:ai:enabled", "group": "1_gitlens", "order": 2}]}}, "gitlens.ai.generateCommitMessage:scm": {"label": "Generate Commit Message with GitLens", "icon": "$(sparkle)", "menus": {"git.commit": [{"when": "gitlens:enabled && !gitlens:readonly && !gitlens:untrusted && gitlens:gk:organization:ai:enabled && config.gitlens.menus.scmRepository.generateCommitMessage", "group": "4_git<PERSON>s", "order": 2}], "scm/title": [{"when": "scmProvider == git && gitlens:enabled && !gitlens:readonly && !gitlens:untrusted && gitlens:gk:organization:ai:enabled && config.gitlens.ai.enabled && config.gitlens.menus.scmRepositoryInline.generateCommitMessage", "group": "navigation", "order": -3}, {"when": "scmProvider == git && gitlens:enabled && !gitlens:readonly && !gitlens:untrusted && gitlens:gk:organization:ai:enabled && config.gitlens.menus.scmRepository.generateCommitMessage", "group": "2_git<PERSON>s", "order": 2}]}}, "gitlens.ai.generateCommits": {"label": "Generate Commits with AI (Experimental)...", "icon": "$(sparkle)", "commandPalette": "gitlens:enabled && !gitlens:untrusted && gitlens:gk:organization:ai:enabled && config.gitlens.ai.experimental.generateCommits.enabled"}, "gitlens.ai.generateCommits:graph": {"label": "Generate Commits with AI (Experimental)", "icon": "$(sparkle)", "menus": {"webview/context": [{"when": "webviewItem == gitlens:wip && !listMultiSelection && !gitlens:readonly && !gitlens:untrusted && gitlens:gk:organization:ai:enabled && config.gitlens.ai.experimental.generateCommits.enabled", "group": "1_gitlens", "order": 2}]}}, "gitlens.ai.generateCommits:views": {"label": "Generate Commits with AI (Experimental)", "icon": "$(sparkle)", "menus": {"view/item/context": [{"when": "viewItem =~ /gitlens:(worktree\\b(?=.*?\\b\\+working\\b)|uncommitted)\\b/ && !listMultiSelection && !gitlens:readonly && !gitlens:untrusted && gitlens:gk:organization:ai:enabled && config.gitlens.ai.experimental.generateCommits.enabled", "group": "3_gitlens_ai", "order": 1}]}}, "gitlens.ai.generateRebase": {"label": "Rebase with AI (Experimental)...", "icon": "$(sparkle)", "commandPalette": "gitlens:enabled && !gitlens:untrusted && gitlens:gk:organization:ai:enabled && config.gitlens.ai.experimental.generateRebase.enabled"}, "gitlens.ai.rebaseOntoCommit:graph": {"label": "AI Rebase Current Branch onto Commit...", "icon": "$(sparkle)", "enablement": "!operationInProgress", "menus": {"webview/context": [{"when": "webviewItem =~ /gitlens:commit\\b/ && !listMultiSelection && !gitlens:readonly && !gitlens:untrusted && !gitlens:hasVirtualFolders && gitlens:gk:organization:ai:enabled && config.gitlens.ai.experimental.generateRebase.enabled", "group": "1_gitlens_actions", "order": 6}]}}, "gitlens.ai.rebaseOntoCommit:views": {"label": "AI Rebase Current Branch onto Commit...", "icon": "$(sparkle)", "enablement": "!operationInProgress", "menus": {"gitlens/commit/file/commit": [{"when": "viewItem =~ /gitlens:file\\b(?=.*?\\b\\+committed\\b)(?=.*?\\b\\+current\\b)/ && !gitlens:readonly && !gitlens:untrusted && !gitlens:hasVirtualFolders && gitlens:gk:organization:ai:enabled && config.gitlens.ai.experimental.generateRebase.enabled", "group": "1_gitlens_actions", "order": 6}], "view/item/context": [{"when": "viewItem =~ /gitlens:commit\\b(?!.*?\\b\\+rebase\\b)/ && !listMultiSelection && !gitlens:readonly && !gitlens:untrusted && !gitlens:hasVirtualFolders && gitlens:gk:organization:ai:enabled && config.gitlens.ai.experimental.generateRebase.enabled", "group": "1_gitlens_actions", "order": 6}]}}, "gitlens.ai.switchProvider": {"label": "Switch AI Provider/Model", "commandPalette": "gitlens:enabled && gitlens:gk:organization:ai:enabled"}, "gitlens.annotations.nextChange": {"label": "Next Change", "icon": "$(arrow-down)"}, "gitlens.annotations.previousChange": {"label": "Previous Change", "icon": "$(arrow-up)"}, "gitlens.applyPatchFromClipboard": {"label": "Apply Copied Changes (Patch)", "commandPalette": "gitlens:enabled && !gitlens:untrusted && !gitlens:hasVirtualFolders"}, "gitlens.associateIssueWithBranch": {"label": "Associate Issue with Branch...", "commandPalette": "gitlens:enabled && !gitlens:readonly && !gitlens:untrusted && !gitlens:hasVirtualFolders"}, "gitlens.browseRepoAtRevision": {"label": "Browse Repository from Revision", "icon": "$(folder-opened)", "enablement": "gitlens:enabled && resourceScheme =~ /^(gitlens|pr)$/", "commandPalette": "!gitlens:hasVirtualFolders && gitlens:enabled && resourceScheme =~ /^(gitlens|git|pr)$/"}, "gitlens.browseRepoAtRevisionInNewWindow": {"label": "Browse Repository from Revision in New Window", "icon": "$(folder-opened)", "enablement": "gitlens:enabled && resourceScheme =~ /^(gitlens|pr)$/", "commandPalette": "!gitlens:hasVirtualFolders && gitlens:enabled && resourceScheme =~ /^(gitlens|git|pr)$/"}, "gitlens.browseRepoBeforeRevision": {"label": "Browse Repository from Before Revision", "icon": "$(folder-opened)", "enablement": "gitlens:enabled && resourceScheme =~ /^(gitlens|pr)$/", "commandPalette": "!gitlens:hasVirtualFolders && gitlens:enabled && resourceScheme =~ /^(gitlens|git|pr)$/"}, "gitlens.browseRepoBeforeRevisionInNewWindow": {"label": "Browse Repository from Before Revision in New Window", "icon": "$(folder-opened)", "enablement": "gitlens:enabled && resourceScheme =~ /^(gitlens|pr)$/", "commandPalette": "!gitlens:hasVirtualFolders && gitlens:enabled && resourceScheme =~ /^(gitlens|git|pr)$/"}, "gitlens.changeBranchMergeTarget": {"label": "Change Branch Merge Target", "commandPalette": "gitlens:enabled && !gitlens:readonly && !gitlens:untrusted && !gitlens:hasVirtualFolders"}, "gitlens.clearFileAnnotations": {"label": "Clear File Annotations", "icon": "$(gitlens-gitlens-filled)", "commandPalette": "resource in gitlens:tabs:blameable && (gitlens:window:annotated || resource in gitlens:tabs:annotated)", "menus": {"editor/title": [{"when": "resource in gitlens:tabs:blameable && (gitlens:window:annotated == computed || (resource in gitlens:tabs:annotated && resource not in gitlens:tabs:annotated:computing)) && config.gitlens.menus.editorGroup.blame", "group": "navigation", "order": 100}], "gitlens/editor/annotations": [{"when": "resource in gitlens:tabs:blameable && (gitlens:window:annotated || resource in gitlens:tabs:annotated)", "group": "1_gitlens", "order": 1}]}}, "gitlens.closeUnchangedFiles": {"label": "Close Unchanged Files", "commandPalette": "gitlens:enabled", "menus": {"scm/resourceGroup/context": [{"when": "scmResourceGroup =~ /^(workingTree|index)$/ && scmProvider == git && gitlens:enabled && config.gitlens.menus.scmGroup.openClose", "group": "3_git<PERSON>s", "order": 2}]}}, "gitlens.compareHeadWith": {"label": "Compare HEAD with...", "icon": "$(compare-changes)", "commandPalette": "gitlens:enabled"}, "gitlens.compareWith": {"label": "Compare References...", "icon": "$(compare-changes)", "commandPalette": "gitlens:enabled"}, "gitlens.compareWorkingWith": {"label": "Compare Working Tree with...", "icon": "$(compare-changes)", "commandPalette": "gitlens:enabled && !gitlens:hasVirtualFolders"}, "gitlens.computingFileAnnotations": {"label": "Computing File Annotations...", "icon": "$(gitlens-gitlens-filled)", "menus": {"editor/title": [{"when": "resource in gitlens:tabs:blameable && (gitlens:window:annotated == computing || resource in gitlens:tabs:annotated:computing) && config.gitlens.menus.editorGroup.blame", "group": "navigation", "order": 100}]}}, "gitlens.connectRemoteProvider": {"label": "Connect Remote Integration", "icon": "$(plug)", "commandPalette": "config.gitlens.integrations.enabled && gitlens:repos:withHostingIntegrations && !gitlens:repos:withHostingIntegrationsConnected", "menus": {"view/item/context": [{"when": "viewItem =~ /gitlens:autolinked:items\\b/ && gitlens:repos:withHostingIntegrations && !gitlens:repos:withHostingIntegrationsConnected && config.gitlens.integrations.enabled", "group": "inline", "order": 98}, {"when": "viewItem =~ /gitlens:autolinked:items\\b/ && gitlens:repos:withHostingIntegrations && !gitlens:repos:withHostingIntegrationsConnected && config.gitlens.integrations.enabled", "group": "6_gitlens_actions", "order": 1}, {"when": "viewItem =~ /gitlens:remote\\b(?=.*?\\b\\+disconnected\\b)/ && config.gitlens.integrations.enabled", "group": "inline", "order": 98}, {"when": "viewItem =~ /gitlens:remote\\b(?=.*?\\b\\+disconnected\\b)/ && !listMultiSelection && config.gitlens.integrations.enabled", "group": "8_gitlens_actions", "order": 2}]}}, "gitlens.copyCurrentBranch": {"label": "Copy Current Branch Name", "icon": "$(copy)", "commandPalette": "gitlens:enabled"}, "gitlens.copyDeepLinkToBranch": {"label": "Copy Link to Branch", "icon": "$(copy)", "menus": {"gitlens/share": [{"when": "viewItem =~ /gitlens:(branch\\b(?=.*?\\b\\+(remote|tracking)\\b)|status:upstream(?!:none))\\b/ && !listMultiSelection", "group": "1_gitlens", "order": 50}]}}, "gitlens.copyDeepLinkToCommit": {"label": "Copy Link to Commit", "icon": "$(copy)", "menus": {"gitlens/share": [{"when": "viewItem =~ /gitlens:(commit|file\\b(?=.*?\\b\\+committed\\b))/ && !listMultiSelection", "group": "1_gitlens", "order": 25}]}}, "gitlens.copyDeepLinkToComparison": {"label": "<PERSON><PERSON> Link to Comparison", "icon": "$(copy)", "menus": {"gitlens/share": [{"when": "viewItem =~ /gitlens:compare:(branch(?=.*?\\b\\+comparing\\b)|results(?!:))\\b/ && !listMultiSelection", "group": "1_gitlens", "order": 25}]}}, "gitlens.copyDeepLinkToFile": {"label": "Copy Link to File", "icon": "$(copy)", "menus": {"editor/context/copy": [{"when": "resourceScheme in gitlens:schemes:trackable && editorTextFocus && config.gitlens.menus.editor.clipboard", "group": "1_gitlens", "order": 3}], "gitlens/editor/lineNumber/context/share": [{"group": "1_gitlens", "order": 2}], "gitlens/share": [{"when": "viewItem =~ /gitlens:file\\b((?=.*?\\b\\+committed\\b)|:results)/ && !listMultiSelection && gitlens:repos:withRemotes", "group": "1_gitlens", "order": 26}]}}, "gitlens.copyDeepLinkToFileAtRevision": {"label": "Copy Link to File at Revision...", "icon": "$(copy)", "menus": {"editor/context/copy": [{"when": "resourceScheme in gitlens:schemes:trackable && resource in gitlens:tabs:tracked && editorTextFocus && config.gitlens.menus.editor.clipboard", "group": "1_gitlens", "order": 4}], "gitlens/editor/lineNumber/context/share": [{"when": "resource in gitlens:tabs:tracked", "group": "1_gitlens", "order": 3}], "gitlens/share": [{"when": "viewItem =~ /gitlens:file\\b((?=.*?\\b\\+committed\\b)|:results)/ && !listMultiSelection && gitlens:repos:withRemotes", "group": "1_gitlens", "order": 27}]}}, "gitlens.copyDeepLinkToLines": {"label": "Copy Link to Code", "icon": "$(copy)", "menus": {"editor/context/copy": [{"when": "resourceScheme in gitlens:schemes:trackable && editorTextFocus && config.gitlens.menus.editor.clipboard && editorHasSelection", "group": "1_gitlens", "order": 2}], "gitlens/editor/lineNumber/context/share": [{"group": "1_gitlens", "order": 1}]}}, "gitlens.copyDeepLinkToRepo": {"label": "Copy Link to Repository", "icon": "$(copy)", "commandPalette": "gitlens:enabled", "menus": {"gitlens/share": [{"when": "viewItem =~ /gitlens:(branch\\b(?=.*?\\b\\+(remote|tracking)\\b)|remote|repo-folder|repository|status:upstream(?!:none))\\b/ && !listMultiSelection", "group": "1_gitlens", "order": 99}]}}, "gitlens.copyDeepLinkToTag": {"label": "<PERSON><PERSON> Link to Tag", "icon": "$(copy)", "menus": {"gitlens/share": [{"when": "viewItem =~ /gitlens:tag\\b/ && !listMultiSelection", "group": "1_gitlens", "order": 50}]}}, "gitlens.copyDeepLinkToWorkspace": {"label": "Copy Link to Workspace", "icon": "$(copy)", "menus": {"gitlens/share": [{"when": "viewItem =~ /gitlens:workspace\\b/ && !listMultiSelection", "group": "1_gitlens", "order": 25}]}}, "gitlens.copyMessageToClipboard": {"label": "Copy Message", "icon": "$(copy)", "commandPalette": "resource in gitlens:tabs:blameable", "menus": {"editor/context/copy": [{"when": "resourceScheme in gitlens:schemes:trackable && resource in gitlens:tabs:tracked && editorTextFocus && config.gitlens.menus.editor.clipboard", "group": "3_git<PERSON>s", "order": 2}], "gitlens/commit/copy": [{"when": "viewItem =~ /gitlens:(?!(commit|file|remote|repo-folder|repository|stash)\\b)/", "group": "1_gitlens", "order": 3}], "view/item/context": [{"when": "viewItem =~ /gitlens:(commit|stash)\\b/ && !listMultiSelection", "group": "7_gitlens_cutcopypaste", "order": 4}, {"when": "viewItem =~ /gitlens:file\\b(?=.*?\\b\\+committed\\b)/ && view =~ /gitlens\\.views\\.(file|line)History/ && !listMultiSelection", "group": "7_gitlens_cutcopypaste", "order": 98}]}}, "gitlens.copyPatchToClipboard": {"label": "Copy Changes (Patch)", "commandPalette": "gitlens:enabled && !gitlens:untrusted && !gitlens:hasVirtualFolders", "menus": {"git.commit": [{"when": "gitlens:enabled && scmProvider == git && config.gitlens.menus.scmRepository.patch", "group": "4_git<PERSON>s", "order": 3}], "scm/resourceFolder/context": [{"when": "scmResourceGroup =~ /^(workingTree|index)$/ && scmProvider == git && gitlens:enabled && config.gitlens.menus.scmGroup.patch", "group": "7_cutcopypaste", "order": 97}], "scm/resourceGroup/context": [{"when": "scmResourceGroup =~ /^(workingTree|index)$/ && scmProvider == git && gitlens:enabled && config.gitlens.menus.scmGroup.patch", "group": "7_cutcopypaste", "order": 97}], "scm/resourceState/context": [{"when": "scmResourceGroup =~ /^(workingTree|index)$/ && scmProvider == git && gitlens:enabled && config.gitlens.menus.scmItem.patch", "group": "7_cutcopypaste", "order": 97}], "scm/title": [{"when": "scmProvider == git && gitlens:enabled && config.gitlens.menus.scmRepository.patch", "group": "2_z_gitlens", "order": 2}], "view/item/context": [{"when": "viewItem =~ /gitlens:(commit|stash)\\b/ && !listMultiSelection && !gitlens:untrusted && !gitlens:hasVirtualFolders", "group": "7_gitlens_cutcopypaste", "order": 97}, {"when": "viewItem =~ /gitlens:compare:(branch|results(?!:))\\b/ && !listMultiSelection && !gitlens:untrusted && !gitlens:hasVirtualFolders", "group": "7_gitlens_cutcopypaste", "order": 97}, {"when": "viewItem =~ /gitlens:file(\\b(?=.*?\\b\\+committed\\b)|:results)/ && !gitlens:untrusted && !gitlens:hasVirtualFolders", "group": "7_gitlens_cutcopypaste", "order": 3}], "webview/context": [{"when": "webviewItem =~ /gitlens:(commit|stash)\\b/ && !listMultiSelection && !gitlens:untrusted && !gitlens:hasVirtualFolders", "group": "7_cutcopypaste", "order": 97}]}}, "gitlens.copyRelativePathToClipboard": {"label": "Copy Relative Path", "icon": "$(copy)", "commandPalette": "gitlens:enabled", "menus": {"editor/context/copy": [{"when": "resourceScheme in gitlens:schemes:trackable && editorTextFocus && config.gitlens.menus.editor.clipboard", "group": "1_gitlens", "order": 1}], "scm/resourceState/context": [{"when": "scmResourceGroup =~ /^(workingTree|index|merge)$/ && scmProvider == git && gitlens:enabled && gitlens:repos:withRemotes && config.gitlens.menus.scmItem.clipboard", "group": "7_cutcopypaste", "order": 98}], "view/item/context": [{"when": "viewItem =~ /gitlens:file\\b/ && !listMultiSelection", "group": "7_gitlens_cutcopypaste", "order": 2}]}}, "gitlens.copyRemoteBranchesUrl": {"label": "Copy Remote Branches URL", "icon": "$(copy)"}, "gitlens.copyRemoteBranchUrl": {"label": "Copy Remote Branch URL", "icon": "$(copy)", "menus": {"gitlens/commit/copy": [{"when": "viewItem =~ /gitlens:branch\\b(?=.*?\\b\\+(tracking|remote)\\b)/", "group": "1_gitlens", "order": 4}], "gitlens/share": [{"when": "viewItem =~ /gitlens:branch\\b(?=.*?\\b\\+(remote|tracking)\\b)/ && !listMultiSelection && gitlens:repos:withRemotes", "group": "2_git<PERSON>s", "order": 50}], "view/item/context": [{"when": "viewItem =~ /gitlens:status:upstream:(?!(missing|none))/ && !listMultiSelection && gitlens:repos:withRemotes", "group": "7_gitlens_cutcopypaste", "order": 1}]}}, "gitlens.copyRemoteCommitUrl": {"label": "Copy Remote Commit URL", "icon": "$(copy)", "commandPalette": "gitlens:repos:withRemotes", "menus": {"editor/context/copy": [{"when": "resourceScheme in gitlens:schemes:trackable && resource in gitlens:tabs:tracked && editorTextFocus && config.gitlens.menus.editor.clipboard", "group": "1_gitlens_remote", "order": 3}], "gitlens/editor/lineNumber/context/share": [{"group": "1_gitlens_remote_commit", "order": 1}]}}, "gitlens.copyRemoteComparisonUrl": {"label": "Copy Remote Comparison URL", "icon": "$(copy)", "menus": {"gitlens/share": [{"when": "viewItem =~ /gitlens:compare:(results(:commits|(?!:))|branch\\b(?=.*?\\b\\+comparing\\b))/ && !listMultiSelection && gitlens:repos:withRemotes", "group": "2_git<PERSON>s", "order": 50}]}}, "gitlens.copyRemoteFileUrlFrom": {"label": "Copy Remote File URL From...", "icon": "$(copy)", "commandPalette": "gitlens:repos:withRemotes", "menus": {"editor/context/copy": [{"when": "resourceScheme in gitlens:schemes:trackable && resource in gitlens:tabs:tracked && editorTextFocus && config.gitlens.menus.editor.clipboard", "group": "1_gitlens_remote", "order": 2}], "editor/title/context": [{"when": "resourceScheme in gitlens:schemes:trackable && gitlens:enabled && gitlens:repos:withRemotes && config.gitlens.menus.editorTab.clipboard", "group": "1_cutcopypaste", "order": 101}], "explorer/context": [{"when": "!explorerResourceIsRoot && gitlens:enabled && gitlens:repos:withRemotes && config.gitlens.menus.explorer.clipboard", "group": "6_copypath", "order": 101}], "gitlens/editor/lineNumber/context/share": [{"group": "1_gitlens_remote", "order": 3}], "gitlens/share": [{"when": "scmResourceGroup =~ /^(workingTree|index|merge)$/ && scmProvider == git && !listMultiSelection && gitlens:enabled && gitlens:repos:withRemotes", "group": "2_git<PERSON>s", "order": 2}, {"when": "viewItem =~ /gitlens:(file\\b(?=.*?\\b\\+committed\\b)|history:(file|line)|status:file)\\b/ && !listMultiSelection && gitlens:repos:withRemotes", "group": "2_git<PERSON>s", "order": 2}], "menuBar/edit/copy": [{"when": "resource in gitlens:tabs:tracked && config.gitlens.menus.editor.clipboard", "group": "1_gitlens", "order": 2}]}}, "gitlens.copyRemoteFileUrlToClipboard": {"label": "Copy Remote File URL", "icon": "$(copy)", "commandPalette": "gitlens:repos:withRemotes", "menus": {"editor/context/copy": [{"when": "resourceScheme in gitlens:schemes:trackable && resource in gitlens:tabs:tracked && editorTextFocus && config.gitlens.menus.editor.clipboard", "group": "1_gitlens_remote", "order": 1}], "gitlens/editor/lineNumber/context/share": [{"group": "1_gitlens_remote", "order": 2}], "menuBar/edit/copy": [{"when": "resource in gitlens:tabs:tracked && config.gitlens.menus.editor.clipboard", "group": "1_gitlens", "order": 1}]}}, "gitlens.copyRemoteFileUrlWithoutRange": {"label": "Copy Remote File URL", "icon": "$(copy)", "menus": {"editor/title/context": [{"when": "resourceScheme in gitlens:schemes:trackable && gitlens:enabled && gitlens:repos:withRemotes && config.gitlens.menus.editorTab.clipboard", "group": "1_cutcopypaste", "order": 100}], "explorer/context": [{"when": "!explorerResourceIsRoot && gitlens:enabled && gitlens:repos:withRemotes && config.gitlens.menus.explorer.clipboard", "group": "6_copypath", "order": 100}], "gitlens/share": [{"when": "scmResourceGroup =~ /^(workingTree|index|merge)$/ && scmProvider == git && !listMultiSelection && gitlens:enabled && gitlens:repos:withRemotes", "group": "2_git<PERSON>s", "order": 1}, {"when": "viewItem =~ /gitlens:(file\\b(?=.*?\\b\\+committed\\b)|history:(file|line)|status:file)\\b/ && !listMultiSelection && gitlens:repos:withRemotes", "group": "2_git<PERSON>s", "order": 1}]}}, "gitlens.copyRemotePullRequestUrl": {"label": "Co<PERSON>ull Request URL", "icon": "$(copy)"}, "gitlens.copyRemoteRepositoryUrl": {"label": "Copy Remote Repository URL", "icon": "$(copy)", "menus": {"gitlens/share": [{"when": "viewItem =~ /gitlens:(remote|repo-folder|repository)\\b/ && !listMultiSelection && gitlens:repos:withRemotes", "group": "2_git<PERSON>s", "order": 99}]}}, "gitlens.copyShaToClipboard": {"label": "Copy SHA", "icon": "$(copy)", "commandPalette": "resource in gitlens:tabs:blameable", "menus": {"editor/context/copy": [{"when": "resourceScheme in gitlens:schemes:trackable && resource in gitlens:tabs:tracked && editorTextFocus && config.gitlens.menus.editor.clipboard", "group": "3_git<PERSON>s", "order": 1}], "gitlens/commit/copy": [{"when": "viewItem =~ /gitlens:(?!(commit|file|remote|repo-folder|repository|stash)\\b)/", "group": "1_gitlens", "order": 2}], "view/item/context": [{"when": "viewItem =~ /gitlens:commit\\b/", "group": "inline", "order": 98, "alt": "gitlens.copyMessageToClipboard"}, {"when": "viewItem =~ /gitlens:(commit|stash)\\b/ && !listMultiSelection", "group": "7_gitlens_cutcopypaste", "order": 3}, {"when": "viewItem =~ /gitlens:file\\b(?=.*?\\b\\+committed\\b)/ && view =~ /gitlens\\.views\\.(fileHistory|lineHistory)\\b/", "group": "inline", "order": 98, "alt": "gitlens.copyMessageToClipboard"}, {"when": "viewItem =~ /gitlens:file\\b(?=.*?\\b\\+committed\\b)/ && view =~ /gitlens\\.views\\.(file|line)History/ && !listMultiSelection", "group": "7_gitlens_cutcopypaste", "order": 97}]}}, "gitlens.copyWorkingChangesToWorktree": {"label": "Copy Working Changes to Worktree...", "commandPalette": "gitlens:enabled && !gitlens:untrusted && !gitlens:hasVirtualFolders"}, "gitlens.copyWorkingChangesToWorktree:views": {"label": "Copy Working Changes to Worktree...", "menus": {"view/item/context": [{"when": "viewItem =~ /gitlens:(worktree\\b(?=.*?\\b\\+working\\b)|uncommitted)\\b/ && !listMultiSelection && !gitlens:readonly && !gitlens:untrusted && !gitlens:hasVirtualFolders", "group": "3_git<PERSON>s", "order": 99}]}}, "gitlens.createCloudPatch": {"label": "Create Patch...", "commandPalette": "gitlens:enabled && gitlens:gk:organization:drafts:enabled && config.gitlens.cloudPatches.enabled", "menus": {"gitlens/commit/file/commit": [{"when": "viewItem =~ /gitlens:file\\b(?=.*?\\b\\+committed\\b)/ && !gitlens:untrusted && !gitlens:hasVirtualFolders && gitlens:gk:organization:drafts:enabled && config.gitlens.cloudPatches.enabled", "group": "1_gitlens_secondary_actions", "order": 3}], "view/item/context": [{"when": "viewItem =~ /gitlens:(commit|stash)\\b/ && !listMultiSelection && !gitlens:untrusted && !gitlens:hasVirtualFolders && gitlens:gk:organization:drafts:enabled && config.gitlens.cloudPatches.enabled", "group": "1_gitlens_actions_1", "order": 3}, {"when": "viewItem =~ /gitlens:compare:results(?!:)\\b/ && !listMultiSelection && !gitlens:untrusted && !gitlens:hasVirtualFolders && gitlens:gk:organization:drafts:enabled && config.gitlens.cloudPatches.enabled", "group": "1_gitlens_secondary_actions", "order": 2}]}}, "gitlens.createPatch": {"label": "Create Patch...", "commandPalette": "false && gitlens:enabled && !gitlens:untrusted && !gitlens:hasVirtualFolders", "menus": {"gitlens/commit/file/commit": [{"when": "false && !gitlens:untrusted && !gitlens:hasVirtualFolders && viewItem =~ /gitlens:file\\b(?=.*?\\b\\+committed\\b)/", "group": "1_gitlens_secondary_actions", "order": 2}], "view/item/context": [{"when": "!listMultiSelection && false && !gitlens:untrusted && !gitlens:hasVirtualFolders && viewItem =~ /gitlens:(commit|stash)\\b/", "group": "1_gitlens_actions_1", "order": 2}, {"when": "!listMultiSelection && false && !gitlens:untrusted && !gitlens:hasVirtualFolders && viewItem =~ /gitlens:compare:results(?!:)\\b/", "group": "1_gitlens_secondary_actions", "order": 1}]}}, "gitlens.createPullRequestOnRemote": {"label": "Create <PERSON>ull Request on Remote", "icon": "$(git-pull-request-create)", "commandPalette": "gitlens:repos:withRemotes"}, "gitlens.diffDirectory": {"label": "Open Directory Compare (difftool) with...", "commandPalette": "gitlens:enabled && !gitlens:hasVirtualFolders", "menus": {"gitlens/scm/resourceGroup/changes": [{"when": "!gitlens:hasVirtualFolders", "group": "2_git<PERSON>s", "order": 3}]}}, "gitlens.diffDirectoryWithHead": {"label": "Open Directory Compare (difftool)", "commandPalette": "gitlens:enabled && !gitlens:hasVirtualFolders", "menus": {"gitlens/scm/resourceGroup/changes": [{"when": "!gitlens:hasVirtualFolders", "group": "2_git<PERSON>s", "order": 2}]}}, "gitlens.diffFolderWithRevision": {"label": "Open Folder Changes with Revision...", "commandPalette": "gitlens:enabled && !gitlens:hasVirtualFolders", "menus": {"gitlens/explorer/changes": [{"when": "explorerResourceIsFolder && !gitlens:hasVirtualFolders", "group": "1_gitlens", "order": 1}], "gitlens/scm/resourceFolder/changes": [{"when": "!gitlens:hasVirtualFolders", "group": "1_gitlens", "order": 1}]}}, "gitlens.diffFolderWithRevisionFrom": {"label": "Open Folder Changes with Branch or Tag...", "commandPalette": "gitlens:enabled && !gitlens:hasVirtualFolders", "menus": {"gitlens/explorer/changes": [{"when": "explorerResourceIsFolder && !gitlens:hasVirtualFolders", "group": "1_gitlens", "order": 2}], "gitlens/scm/resourceFolder/changes": [{"when": "!gitlens:hasVirtualFolders", "group": "1_gitlens", "order": 2}]}}, "gitlens.diffLineWithPrevious": {"label": "Open Line Changes with Previous Revision", "commandPalette": "resource in gitlens:tabs:blameable", "menus": {"gitlens/editor/context/changes": [{"when": "resource in gitlens:tabs:tracked && editorTextFocus", "group": "1_gitlens", "order": 1}], "gitlens/editor/lineNumber/context/changes": [{"group": "1_gitlens", "order": 1}]}, "keybindings": [{"key": "ctrl+shift+g shift+,", "when": "config.gitlens.keymap == chorded && editorTextFocus && resource in gitlens:tabs:tracked", "mac": "cmd+alt+g shift+,"}, {"key": "shift+alt+,", "when": "config.gitlens.keymap == alternate && editorTextFocus && resource in gitlens:tabs:tracked"}]}, "gitlens.diffLineWithWorking": {"label": "Open Line Changes with Working File", "commandPalette": "resource in gitlens:tabs:blameable", "menus": {"gitlens/editor/context/changes": [{"when": "resource in gitlens:tabs:tracked && editorTextFocus", "group": "1_gitlens", "order": 2}], "gitlens/editor/lineNumber/context/changes": [{"group": "1_gitlens", "order": 2}]}}, "gitlens.diffWithNext": {"label": "Open Changes with Next Revision", "icon": "$(gitlens-next-commit)", "enablement": "gitlens:enabled && resourceScheme =~ /^(gitlens|git|pr)$/", "commandPalette": "gitlens:enabled && resourceScheme =~ /^(gitlens|git|pr)$/", "menus": {"editor/title": [{"when": "resource in gitlens:tabs:tracked && config.gitlens.menus.editorGroup.compare", "group": "navigation", "order": 99}]}, "keybindings": [{"key": "alt+.", "when": "config.gitlens.keymap == alternate && editorTextFocus && gitlens:enabled && resourceScheme =~ /^(gitlens|git|pr)$/"}, {"key": "ctrl+shift+g .", "when": "config.gitlens.keymap == chorded && editorTextFocus && gitlens:enabled && resourceScheme =~ /^(gitlens|git|pr)$/", "mac": "cmd+alt+g ."}]}, "gitlens.diffWithPrevious": {"label": "Open Changes with Previous Revision", "icon": "$(gitlens-prev-commit)", "commandPalette": "resource in gitlens:tabs:tracked", "menus": {"editor/title": [{"when": "resource in gitlens:tabs:tracked && config.gitlens.menus.editorGroup.compare", "group": "navigation", "order": 97, "alt": "gitlens.diffWithRevision"}], "gitlens/editor/changes": [{"group": "1_gitlens", "order": 1}], "gitlens/editor/context/changes": [{"group": "2_git<PERSON>s", "order": 1}], "gitlens/explorer/changes": [{"when": "!explorerResourceIsFolder", "group": "1_gitlens", "order": 1}]}, "keybindings": [{"key": "alt+,", "when": "config.gitlens.keymap == alternate && editorTextFocus && resource in gitlens:tabs:tracked"}, {"key": "ctrl+shift+g ,", "when": "config.gitlens.keymap == chorded && editorTextFocus && resource in gitlens:tabs:tracked", "mac": "cmd+alt+g ,"}]}, "gitlens.diffWithRevision": {"label": "Open Changes with Revision...", "icon": "$(gitlens-prev-commit-menu)", "commandPalette": "resource in gitlens:tabs:tracked", "menus": {"gitlens/commit/file/changes": [{"group": "1_gitlens", "order": 3}], "gitlens/editor/changes": [{"group": "1_gitlens", "order": 3}], "gitlens/editor/context/changes": [{"group": "2_git<PERSON>s", "order": 3}], "gitlens/explorer/changes": [{"when": "!explorerResourceIsFolder", "group": "1_gitlens", "order": 2}], "gitlens/scm/resourceState/changes": [{"when": "!gitlens:hasVirtualFolders", "group": "1_gitlens", "order": 1}]}}, "gitlens.diffWithRevisionFrom": {"label": "Open Changes with Branch or Tag...", "commandPalette": "resource in gitlens:tabs:tracked", "menus": {"gitlens/commit/file/changes": [{"group": "1_gitlens", "order": 4}], "gitlens/editor/changes": [{"group": "1_gitlens", "order": 4}], "gitlens/editor/context/changes": [{"group": "2_git<PERSON>s", "order": 4}], "gitlens/explorer/changes": [{"when": "!explorerResourceIsFolder", "group": "1_gitlens", "order": 3}], "gitlens/scm/resourceState/changes": [{"when": "!gitlens:hasVirtualFolders", "group": "1_gitlens", "order": 2}]}}, "gitlens.diffWithWorking": {"label": "Open Changes with Working File", "icon": "$(gitlens-compare-ref-working)", "commandPalette": "gitlens:enabled && resourceScheme =~ /^(gitlens|git|pr)$/", "menus": {"editor/title": [{"when": "resourceScheme =~ /^(gitlens|pr)$/ && gitlens:enabled", "group": "navigation", "order": -99}], "gitlens/editor/changes": [{"when": "resourceScheme == gitlens", "group": "1_gitlens", "order": 2}], "gitlens/editor/context/changes": [{"when": "resourceScheme == gitlens", "group": "2_git<PERSON>s", "order": 2}]}, "keybindings": [{"key": "ctrl+shift+g shift+.", "when": "config.gitlens.keymap == chorded && editorTextFocus && gitlens:enabled && resourceScheme =~ /^(gitlens|git|pr)$/", "mac": "cmd+alt+g shift+."}, {"key": "shift+alt+.", "when": "config.gitlens.keymap == alternate && editorTextFocus && gitlens:enabled && resourceScheme =~ /^(gitlens|git|pr)$/"}]}, "gitlens.disableDebugLogging": {"label": "Disable Debug Logging", "commandPalette": "config.gitlens.outputLevel == debug"}, "gitlens.disableRebaseEditor": {"label": "Disable Interactive Rebase Editor", "commandPalette": "gitlens:enabled"}, "gitlens.disconnectRemoteProvider": {"label": "Disconnect Remote Integration", "icon": "$(gitlens-unplug)", "commandPalette": "config.gitlens.integrations.enabled && gitlens:repos:withHostingIntegrationsConnected", "menus": {"view/item/context": [{"when": "viewItem =~ /gitlens:remote\\b(?=.*?\\b\\+connected\\b)/ && config.gitlens.integrations.enabled", "group": "inline", "order": 98}, {"when": "viewItem =~ /gitlens:remote\\b(?=.*?\\b\\+connected\\b)/ && !listMultiSelection && config.gitlens.integrations.enabled", "group": "8_gitlens_actions", "order": 2}]}}, "gitlens.enableDebugLogging": {"label": "Enable Debug Logging", "commandPalette": "config.gitlens.outputLevel != debug"}, "gitlens.enableRebaseEditor": {"label": "Enable Interactive Rebase Editor", "commandPalette": "gitlens:enabled"}, "gitlens.externalDiff": {"label": "Open Changes (difftool)", "commandPalette": "!gitlens:hasVirtualFolders && resource in gitlens:tabs:tracked", "menus": {"gitlens/commit/file/changes": [{"when": "viewItem =~ /gitlens:file\\b(?!.*?\\b\\+conflicted\\b)/ && !gitlens:readonly && !gitlens:untrusted && !gitlens:hasVirtualFolders", "group": "1_git<PERSON>s_", "order": 1}], "gitlens/scm/resourceState/changes": [{"when": "!gitlens:readonly && !gitlens:untrusted && !gitlens:hasVirtualFolders", "group": "2_git<PERSON>s", "order": 1}]}}, "gitlens.externalDiffAll": {"label": "Open All Changes (difftool)", "commandPalette": "gitlens:enabled && !gitlens:hasVirtualFolders", "menus": {"gitlens/scm/resourceGroup/changes": [{"when": "!gitlens:readonly && !gitlens:untrusted && !gitlens:hasVirtualFolders", "group": "2_git<PERSON>s", "order": 1}]}}, "gitlens.fetchRepositories": {"label": "<PERSON>tch", "icon": "$(repo-fetch)", "enablement": "!operationInProgress", "commandPalette": "gitlens:repos:withRemotes && !gitlens:readonly && !gitlens:untrusted && !gitlens:hasVirtualFolders", "menus": {"gitlens/views/grouped/commits": [{"when": "gitlens:views:scm:grouped:view == commits && !gitlens:readonly && !gitlens:untrusted && !gitlens:hasVirtualFolders && gitlens:repos:withRemotes", "group": "2_gitlens_actions", "order": 3}], "gitlens/views/grouped/repositories": [{"when": "gitlens:views:scm:grouped:view == repositories && !gitlens:readonly && !gitlens:untrusted && !gitlens:hasVirtualFolders && gitlens:repos:withRemotes", "group": "2_gitlens_actions", "order": 3}], "view/title": [{"when": "view == gitlens.views.commits && !gitlens:readonly && !gitlens:untrusted && !gitlens:hasVirtualFolders && gitlens:repos:withRemotes", "group": "navigation", "order": 3}, {"when": "view == gitlens.views.repositories && !gitlens:readonly && !gitlens:untrusted && !gitlens:hasVirtualFolders && gitlens:repos:withRemotes", "group": "navigation", "order": 3}]}}, "gitlens.getStarted": {"label": "Get Started", "commandPalette": true, "menus": {"extension/context": [{"when": "extension =~ /^eamodio.gitlens?$/ && extensionStatus == installed", "group": "9_git<PERSON>s", "order": 1}]}}, "gitlens.ghpr.views.openOrCreateWorktree": {"label": "Checkout Pull Request in Worktree (GitLens)...", "enablement": "!operationInProgress", "menus": {"view/item/context": [{"when": "viewItem =~ /pullrequest(:local)?:nonactive|description/ && view == pr:github && !listMultiSelection && !gitlens:readonly && !gitlens:untrusted && !gitlens:hasVirtualFolders && config.gitlens.menus.ghpr.worktree", "group": "2_git<PERSON>s", "order": 1}]}}, "gitlens.gitCommands": {"label": "Git Command Palette", "commandPalette": "!gitlens:disabled", "keybindings": [{"key": "alt+/", "when": "config.gitlens.keymap == alternate && !gitlens:disabled"}, {"key": "ctrl+shift+g /", "when": "config.gitlens.keymap == chorded && !gitlens:disabled", "mac": "cmd+alt+g /"}]}, "gitlens.gitCommands.branch": {"label": "Git Branch...", "commandPalette": "!gitlens:disabled && !gitlens:readonly && !gitlens:untrusted && !gitlens:hasVirtualFolders"}, "gitlens.gitCommands.branch.create": {"label": "Git Create Branch...", "commandPalette": "!gitlens:disabled && !gitlens:readonly && !gitlens:untrusted && !gitlens:hasVirtualFolders"}, "gitlens.gitCommands.branch.delete": {"label": "Git Delete Branch...", "commandPalette": "!gitlens:disabled && !gitlens:readonly && !gitlens:untrusted && !gitlens:hasVirtualFolders"}, "gitlens.gitCommands.branch.prune": {"label": "Git Prune Branches...", "commandPalette": "!gitlens:disabled && !gitlens:readonly && !gitlens:untrusted && !gitlens:hasVirtualFolders"}, "gitlens.gitCommands.branch.rename": {"label": "Git Rename Branch...", "commandPalette": "!gitlens:disabled && !gitlens:readonly && !gitlens:untrusted && !gitlens:hasVirtualFolders"}, "gitlens.gitCommands.checkout": {"label": "Git Checkout...", "commandPalette": "!gitlens:disabled && !gitlens:readonly && !gitlens:untrusted && !gitlens:hasVirtualFolders"}, "gitlens.gitCommands.cherryPick": {"label": "Git <PERSON> Pick...", "commandPalette": "!gitlens:disabled && !gitlens:readonly && !gitlens:untrusted && !gitlens:hasVirtualFolders"}, "gitlens.gitCommands.history": {"label": "Git History (log)...", "commandPalette": "!gitlens:disabled && !gitlens:readonly && !gitlens:untrusted && !gitlens:hasVirtualFolders"}, "gitlens.gitCommands.merge": {"label": "<PERSON>it <PERSON>...", "commandPalette": "!gitlens:disabled && !gitlens:readonly && !gitlens:untrusted && !gitlens:hasVirtualFolders"}, "gitlens.gitCommands.rebase": {"label": "Git Rebase...", "commandPalette": "!gitlens:disabled && !gitlens:readonly && !gitlens:untrusted && !gitlens:hasVirtualFolders"}, "gitlens.gitCommands.remote": {"label": "Git Remote...", "commandPalette": "!gitlens:disabled && !gitlens:readonly && !gitlens:untrusted && !gitlens:hasVirtualFolders"}, "gitlens.gitCommands.remote.add": {"label": "Git Add Remote...", "commandPalette": "!gitlens:disabled && !gitlens:readonly && !gitlens:untrusted && !gitlens:hasVirtualFolders"}, "gitlens.gitCommands.remote.prune": {"label": "<PERSON><PERSON> <PERSON><PERSON>e Remote...", "commandPalette": "!gitlens:disabled && !gitlens:readonly && !gitlens:untrusted && !gitlens:hasVirtualFolders"}, "gitlens.gitCommands.remote.remove": {"label": "Git Remove Remote...", "commandPalette": "!gitlens:disabled && !gitlens:readonly && !gitlens:untrusted && !gitlens:hasVirtualFolders"}, "gitlens.gitCommands.reset": {"label": "Git Reset...", "commandPalette": "!gitlens:disabled && !gitlens:readonly && !gitlens:untrusted && !gitlens:hasVirtualFolders"}, "gitlens.gitCommands.revert": {"label": "Git Revert...", "commandPalette": "!gitlens:disabled && !gitlens:readonly && !gitlens:untrusted && !gitlens:hasVirtualFolders"}, "gitlens.gitCommands.show": {"label": "Git Show...", "commandPalette": "!gitlens:disabled && !gitlens:readonly && !gitlens:untrusted && !gitlens:hasVirtualFolders"}, "gitlens.gitCommands.stash": {"label": "Git <PERSON>ash...", "commandPalette": "!gitlens:disabled && !gitlens:readonly && !gitlens:untrusted && !gitlens:hasVirtualFolders"}, "gitlens.gitCommands.stash.drop": {"label": "Git Drop Stash...", "commandPalette": "!gitlens:disabled && !gitlens:readonly && !gitlens:untrusted && !gitlens:hasVirtualFolders"}, "gitlens.gitCommands.stash.list": {"label": "Git Stash List...", "commandPalette": "!gitlens:disabled && !gitlens:readonly && !gitlens:untrusted && !gitlens:hasVirtualFolders"}, "gitlens.gitCommands.stash.pop": {"label": "Git Pop Stash...", "commandPalette": "!gitlens:disabled && !gitlens:readonly && !gitlens:untrusted && !gitlens:hasVirtualFolders"}, "gitlens.gitCommands.stash.push": {"label": "<PERSON><PERSON>...", "commandPalette": "!gitlens:disabled && !gitlens:readonly && !gitlens:untrusted && !gitlens:hasVirtualFolders"}, "gitlens.gitCommands.stash.rename": {"label": "Git <PERSON>...", "commandPalette": "!gitlens:disabled && !gitlens:readonly && !gitlens:untrusted && !gitlens:hasVirtualFolders"}, "gitlens.gitCommands.status": {"label": "Git Status...", "commandPalette": "!gitlens:disabled && !gitlens:readonly && !gitlens:untrusted && !gitlens:hasVirtualFolders"}, "gitlens.gitCommands.switch": {"label": "Git Switch to...", "commandPalette": "!gitlens:disabled && !gitlens:readonly && !gitlens:untrusted && !gitlens:hasVirtualFolders"}, "gitlens.gitCommands.tag": {"label": "Git Tag...", "commandPalette": "!gitlens:disabled && !gitlens:readonly && !gitlens:untrusted && !gitlens:hasVirtualFolders"}, "gitlens.gitCommands.tag.create": {"label": "Git Create Tag...", "commandPalette": "!gitlens:disabled && !gitlens:readonly && !gitlens:untrusted && !gitlens:hasVirtualFolders"}, "gitlens.gitCommands.tag.delete": {"label": "Git Delete Tag...", "commandPalette": "!gitlens:disabled && !gitlens:readonly && !gitlens:untrusted && !gitlens:hasVirtualFolders"}, "gitlens.gitCommands.worktree": {"label": "Git <PERSON>...", "commandPalette": "!gitlens:disabled && !gitlens:readonly && !gitlens:untrusted && !gitlens:hasVirtualFolders"}, "gitlens.gitCommands.worktree.create": {"label": "Git Create Worktree...", "commandPalette": "!gitlens:disabled && !gitlens:readonly && !gitlens:untrusted && !gitlens:hasVirtualFolders"}, "gitlens.gitCommands.worktree.delete": {"label": "Git Delete Worktree...", "commandPalette": "!gitlens:disabled && !gitlens:readonly && !gitlens:untrusted && !gitlens:hasVirtualFolders"}, "gitlens.gitCommands.worktree.open": {"label": "Git Open Worktree...", "commandPalette": "!gitlens:disabled && !gitlens:readonly && !gitlens:untrusted && !gitlens:hasVirtualFolders"}, "gitlens.gk.switchOrganization": {"label": "Switch Organization...", "commandPalette": "gitlens:gk:hasOrganizations"}, "gitlens.graph.addAuthor": {"label": "Add as Co-author", "icon": "$(person-add)", "menus": {"webview/context": [{"when": "webviewItem =~ /gitlens:contributor\\b(?!.*?\\b\\+current\\b)/ && !gitlens:readonly && !gitlens:untrusted && !gitlens:hasVirtualFolders", "group": "1_gitlens_actions", "order": 2}]}}, "gitlens.graph.associateIssueWithBranch": {"label": "Associate Issue with Branch...", "enablement": "!operationInProgress", "menus": {"webview/context": [{"when": "webviewItem =~ /gitlens:branch\\b(?!.*?\\b\\+remote\\b)/ && !gitlens:readonly && !gitlens:untrusted && !gitlens:hasVirtualFolders", "group": "1_gitlens_actions_2", "order": 1}]}}, "gitlens.graph.cherryPick": {"label": "Cherry Pick Commit...", "enablement": "!operationInProgress", "menus": {"webview/context": [{"when": "webviewItem =~ /gitlens:commit\\b(?!.*?\\b\\+(current|rebase)\\b)/ && !listMultiSelection && !gitlens:readonly && !gitlens:untrusted && !gitlens:hasVirtualFolders", "group": "1_gitlens_actions", "order": 1}]}}, "gitlens.graph.cherryPick.multi": {"label": "Cherry Pick Commits...", "enablement": "!operationInProgress", "menus": {"webview/context": [{"when": "webviewItems =~ /gitlens:commit\\b(?!.*?\\b\\+(current|rebase)\\b)/ && listMultiSelection && !gitlens:readonly && !gitlens:untrusted && !gitlens:hasVirtualFolders", "group": "1_gitlens_actions", "order": 1}]}}, "gitlens.graph.columnAuthorOff": {"label": "Hide Author Col<PERSON>n", "enablement": "webviewItemValue =~ /\\bcolumns:canHide\\b/", "menus": {"webview/context": [{"when": "webviewItem =~ /gitlens:graph:(columns|settings)\\b/ && webviewItemValue =~ /\\bcolumn:author:visible\\b/", "group": "1_columns", "order": 1}]}}, "gitlens.graph.columnAuthorOn": {"label": "Show Author <PERSON><PERSON><PERSON>", "menus": {"webview/context": [{"when": "webviewItem =~ /gitlens:graph:(columns|settings)\\b/ && webviewItemValue =~ /\\bcolumn:author:hidden\\b/", "group": "1_columns", "order": 1}]}}, "gitlens.graph.columnChangesOff": {"label": "Hide Changes Column", "enablement": "webviewItemValue =~ /\\bcolumns:canHide\\b/", "menus": {"webview/context": [{"when": "webviewItem =~ /gitlens:graph:(columns|settings)\\b/ && webviewItemValue =~ /\\bcolumn:changes:visible\\b/", "group": "1_columns", "order": 3}]}}, "gitlens.graph.columnChangesOn": {"label": "Show Changes Column", "menus": {"webview/context": [{"when": "webviewItem =~ /gitlens:graph:(columns|settings)\\b/ && webviewItemValue =~ /\\bcolumn:changes:hidden\\b/", "group": "1_columns", "order": 3}]}}, "gitlens.graph.columnDateTimeOff": {"label": "Hide Date Column", "enablement": "webviewItemValue =~ /\\bcolumns:canHide\\b/", "menus": {"webview/context": [{"when": "webviewItem =~ /gitlens:graph:(columns|settings)\\b/ && webviewItemValue =~ /\\bcolumn:datetime:visible\\b/", "group": "1_columns", "order": 5}]}}, "gitlens.graph.columnDateTimeOn": {"label": "Show Date Column", "menus": {"webview/context": [{"when": "webviewItem =~ /gitlens:graph:(columns|settings)\\b/ && webviewItemValue =~ /\\bcolumn:datetime:hidden\\b/", "group": "1_columns", "order": 5}]}}, "gitlens.graph.columnGraphCompact": {"label": "Use Compact Graph Column", "menus": {"webview/context": [{"when": "webviewItem =~ /gitlens:graph:(columns|settings)\\b/ && webviewItemValue =~ /\\bcolumn:graph:visible(?![^,]*\\+compact\\b)/", "group": "2_columns", "order": 1}]}}, "gitlens.graph.columnGraphDefault": {"label": "Use Expanded Graph Column", "menus": {"webview/context": [{"when": "webviewItem =~ /gitlens:graph:(columns|settings)\\b/ && webviewItemValue =~ /\\bcolumn:graph:visible[^,]*\\+compact\\b/", "group": "2_columns", "order": 1}]}}, "gitlens.graph.columnGraphOff": {"label": "Hide Graph Column", "enablement": "webviewItemValue =~ /\\bcolumns:canHide\\b/", "menus": {"webview/context": [{"when": "webviewItem =~ /gitlens:graph:(columns|settings)\\b/ && webviewItemValue =~ /\\bcolumn:graph:visible\\b/", "group": "1_columns", "order": 6}]}}, "gitlens.graph.columnGraphOn": {"label": "Show Graph Column", "menus": {"webview/context": [{"when": "webviewItem =~ /gitlens:graph:(columns|settings)\\b/ && webviewItemValue =~ /\\bcolumn:graph:hidden\\b/", "group": "1_columns", "order": 6}]}}, "gitlens.graph.columnMessageOff": {"label": "Hide Commit Message Column", "enablement": "webviewItemValue =~ /\\bcolumns:canHide\\b/", "menus": {"webview/context": [{"when": "webviewItem =~ /gitlens:graph:(columns|settings)\\b/ && webviewItemValue =~ /\\bcolumn:message:visible\\b/", "group": "1_columns", "order": 4}]}}, "gitlens.graph.columnMessageOn": {"label": "Show Commit Message Column", "menus": {"webview/context": [{"when": "webviewItem =~ /gitlens:graph:(columns|settings)\\b/ && webviewItemValue =~ /\\bcolumn:message:hidden\\b/", "group": "1_columns", "order": 4}]}}, "gitlens.graph.columnRefOff": {"label": "Hide Branch / Tag Column", "enablement": "webviewItemValue =~ /\\bcolumns:canHide\\b/", "menus": {"webview/context": [{"when": "webviewItem =~ /gitlens:graph:(columns|settings)\\b/ && webviewItemValue =~ /\\bcolumn:ref:visible\\b/", "group": "1_columns", "order": 2}]}}, "gitlens.graph.columnRefOn": {"label": "Show Branch / Tag Column", "menus": {"webview/context": [{"when": "webviewItem =~ /gitlens:graph:(columns|settings)\\b/ && webviewItemValue =~ /\\bcolumn:ref:hidden\\b/", "group": "1_columns", "order": 2}]}}, "gitlens.graph.columnShaOff": {"label": "Hide SHA Column", "enablement": "webviewItemValue =~ /\\bcolumns:canHide\\b/", "menus": {"webview/context": [{"when": "webviewItem =~ /gitlens:graph:(columns|settings)\\b/ && webviewItemValue =~ /\\bcolumn:sha:visible\\b/", "group": "1_columns", "order": 7}]}}, "gitlens.graph.columnShaOn": {"label": "Show SHA Column", "menus": {"webview/context": [{"when": "webviewItem =~ /gitlens:graph:(columns|settings)\\b/ && webviewItemValue =~ /\\bcolumn:sha:hidden\\b/", "group": "1_columns", "order": 7}]}}, "gitlens.graph.commitViaSCM": {"label": "Commit via Source Control...", "menus": {"webview/context": [{"when": "webviewItem == gitlens:wip && !listMultiSelection", "group": "1_gitlens", "order": 1}]}}, "gitlens.graph.compareAncestryWithWorking": {"label": "Compare Working Tree to Common Base", "menus": {"webview/context": [{"when": "webviewItem =~ /gitlens:branch\\b(?!.*?\\b\\+current\\b)/ && !gitlens:hasVirtualFolders", "group": "4_gitlens_compare", "order": 5}]}}, "gitlens.graph.compareBranchWithHead": {"label": "Compare with HEAD", "icon": "$(compare-changes)", "menus": {"webview/context": [{"when": "webviewItem =~ /gitlens:branch\\b(?!.*?\\b\\+current\\b)/ && !listMultiSelection", "group": "4_gitlens_compare", "order": 2}]}}, "gitlens.graph.compareSelectedCommits.multi": {"label": "Compare Selected Commits", "icon": "$(compare-changes)", "menus": {"webview/context": [{"when": "webviewItems =~ /gitlens:commit\\b/ && listDoubleSelection", "group": "4_gitlens_compare", "order": 2}]}}, "gitlens.graph.compareWithHead": {"label": "Compare to/from HEAD", "icon": "$(compare-changes)", "menus": {"webview/context": [{"when": "webviewItem =~ /gitlens:(commit|stash|tag)\\b/ && !listMultiSelection", "group": "4_gitlens_compare", "order": 2}]}}, "gitlens.graph.compareWithMergeBase": {"label": "Compare with Common Base", "menus": {"webview/context": [{"when": "webviewItem =~ /gitlens:branch\\b(?!.*?\\b\\+current\\b)/", "group": "4_gitlens_compare", "order": 3}]}}, "gitlens.graph.compareWithUpstream": {"label": "Compare with Upstream", "menus": {"webview/context": [{"when": "webviewItem =~ /gitlens:branch\\b(?=.*?\\b\\+tracking\\b)/ && !gitlens:hasVirtualFolders", "group": "4_gitlens_compare", "order": 1}]}}, "gitlens.graph.compareWithWorking": {"label": "Compare Working Tree to Here", "icon": "$(gitlens-compare-ref-working)", "menus": {"webview/context": [{"when": "webviewItem =~ /gitlens:(branch|commit|stash|tag)\\b/ && !listMultiSelection && !gitlens:hasVirtualFolders", "group": "4_gitlens_compare", "order": 4}]}}, "gitlens.graph.copy": {"label": "Copy", "icon": "$(copy)", "menus": {"webview/context": [{"when": "webviewItem =~ /gitlens:(branch|commit|contributor|launchpad:item|pullrequest|stash|tag)\\b/", "group": "7_gitlens_cutcopypaste", "order": 1}]}}, "gitlens.graph.copyDeepLinkToBranch": {"label": "Copy Link to Branch", "icon": "$(copy)", "menus": {"gitlens/share": [{"when": "webviewItem =~ /gitlens:branch\\b(?=.*?\\b\\+(remote|tracking)\\b)/ && !listMultiSelection", "group": "1_gitlens", "order": 50}]}}, "gitlens.graph.copyDeepLinkToCommit": {"label": "Copy Link to Commit", "icon": "$(copy)", "menus": {"gitlens/share": [{"when": "webviewItem =~ /gitlens:commit\\b/ && !listMultiSelection", "group": "1_gitlens", "order": 25}]}}, "gitlens.graph.copyDeepLinkToRepo": {"label": "Copy Link to Repository", "icon": "$(copy)", "menus": {"gitlens/share": [{"when": "webviewItem =~ /gitlens:branch\\b(?=.*?\\b\\+(remote|tracking)\\b)/ && !listMultiSelection", "group": "1_gitlens", "order": 99}]}}, "gitlens.graph.copyDeepLinkToTag": {"label": "<PERSON><PERSON> Link to Tag", "icon": "$(copy)", "menus": {"gitlens/share": [{"when": "webviewItem =~ /gitlens:tag\\b/ && !listMultiSelection", "group": "1_gitlens", "order": 50}]}}, "gitlens.graph.copyMessage": {"label": "Copy Message", "icon": "$(copy)", "menus": {"webview/context": [{"when": "webviewItem =~ /gitlens:(branch|commit|stash|tag)\\b/ && !listMultiSelection", "group": "7_gitlens_cutcopypaste", "order": 3}]}}, "gitlens.graph.copyRemoteBranchUrl": {"label": "Copy Remote Branch URL", "icon": "$(copy)", "menus": {"gitlens/share": [{"when": "webviewItem =~ /gitlens:branch\\b(?=.*?\\b\\+(remote|tracking)\\b)/ && !listMultiSelection && gitlens:repos:withRemotes", "group": "2_git<PERSON>s", "order": 50}]}}, "gitlens.graph.copyRemoteCommitUrl": {"label": "Copy Remote Commit URL", "icon": "$(copy)", "menus": {"gitlens/share": [{"when": "webviewItem =~ /gitlens:commit\\b/ && !listMultiSelection && gitlens:repos:withRemotes", "group": "2_git<PERSON>s", "order": 25}]}}, "gitlens.graph.copyRemoteCommitUrl.multi": {"label": "Copy Remote Commit URLs", "icon": "$(copy)", "menus": {"gitlens/share": [{"when": "webviewItems =~ /gitlens:commit\\b/ && listMultiSelection && gitlens:repos:withRemotes", "group": "2_git<PERSON>s", "order": 25}]}}, "gitlens.graph.copySha": {"label": "Copy SHA", "icon": "$(copy)", "menus": {"webview/context": [{"when": "webviewItem =~ /gitlens:(branch|commit|stash|tag)\\b/ && !listMultiSelection", "group": "7_gitlens_cutcopypaste", "order": 2}]}}, "gitlens.graph.copyWorkingChangesToWorktree": {"label": "Copy Working Changes to Worktree...", "menus": {"webview/context": [{"when": "webviewItem == gitlens:wip && !listMultiSelection && !gitlens:readonly && !gitlens:untrusted && !gitlens:hasVirtualFolders", "group": "1_gitlens_actions", "order": 4}]}}, "gitlens.graph.createBranch": {"label": "Create Branch...", "icon": "$(add)", "enablement": "!operationInProgress", "menus": {"webview/context": [{"when": "webviewItem =~ /gitlens:branch\\b/ && !gitlens:readonly && !gitlens:untrusted && !gitlens:hasVirtualFolders", "group": "1_gitlens_actions_3", "order": 1}, {"when": "webviewItem =~ /gitlens:commit\\b/ && !listMultiSelection && !gitlens:readonly && !gitlens:untrusted && !gitlens:hasVirtualFolders", "group": "1_gitlens_actions_1", "order": 1}, {"when": "webviewItem =~ /gitlens:tag\\b/ && !gitlens:readonly && !gitlens:untrusted && !gitlens:hasVirtualFolders", "group": "1_gitlens_actions_3", "order": 4}]}}, "gitlens.graph.createCloudPatch": {"label": "Create Patch...", "menus": {"webview/context": [{"when": "webviewItem =~ /gitlens:(commit|stash)\\b/ && !listMultiSelection && !gitlens:untrusted && !gitlens:hasVirtualFolders && gitlens:gk:organization:drafts:enabled && config.gitlens.cloudPatches.enabled", "group": "1_gitlens_actions_1", "order": 3}]}}, "gitlens.graph.createPatch": {"label": "Create Patch...", "menus": {"webview/context": [{"when": "!listMultiSelection && false && !gitlens:untrusted && !gitlens:hasVirtualFolders && webviewItem =~ /gitlens:(commit|stash)\\b/", "group": "1_gitlens_actions_1", "order": 2}]}}, "gitlens.graph.createPullRequest": {"label": "Create Pull Request...", "icon": "$(git-pull-request-create)", "menus": {"webview/context": [{"when": "webviewItem =~ /gitlens:branch\\b(?=.*?\\b\\+(tracking|remote)\\b)/ && gitlens:action:createPullRequest && gitlens:repos:withRemotes", "group": "1_gitlens_actions_3", "order": 4}]}}, "gitlens.graph.createTag": {"label": "Create Tag...", "icon": "$(add)", "enablement": "!operationInProgress", "menus": {"webview/context": [{"when": "webviewItem =~ /gitlens:branch\\b/ && !gitlens:readonly && !gitlens:untrusted && !gitlens:hasVirtualFolders", "group": "1_gitlens_actions_3", "order": 2}, {"when": "webviewItem =~ /gitlens:commit\\b/ && !listMultiSelection && !gitlens:readonly && !gitlens:untrusted && !gitlens:hasVirtualFolders", "group": "1_gitlens_actions_1", "order": 4}]}}, "gitlens.graph.createWorktree": {"label": "Create Worktree...", "icon": "$(add)", "enablement": "!operationInProgress", "menus": {"webview/context": [{"when": "webviewItem =~ /gitlens:branch\\b/ && !gitlens:readonly && !gitlens:untrusted && !gitlens:hasVirtualFolders", "group": "1_gitlens_actions_3", "order": 3}]}}, "gitlens.graph.deleteBranch": {"label": "Delete Branch...", "icon": "$(trash)", "enablement": "!operationInProgress", "menus": {"webview/context": [{"when": "webviewItem =~ /gitlens:branch\\b(?!.*?\\b\\+(current|checkedout)\\b)/ && !gitlens:readonly && !gitlens:untrusted && !gitlens:hasVirtualFolders", "group": "1_gitlens_actions_2", "order": 3}]}}, "gitlens.graph.deleteTag": {"label": "Delete Tag...", "icon": "$(trash)", "enablement": "!operationInProgress", "menus": {"webview/context": [{"when": "webviewItem == gitlens:tag && !gitlens:readonly && !gitlens:untrusted && !gitlens:hasVirtualFolders", "group": "1_gitlens_actions", "order": 3}]}}, "gitlens.graph.fetch": {"label": "<PERSON>tch", "icon": "$(repo-fetch)", "enablement": "!operationInProgress", "menus": {"webview/context": [{"when": "webviewItem =~ /gitlens:branch\\b(?=.*?\\b\\+(remote|tracking)\\b)(?!.*?\\b\\+closed\\b)/ && !gitlens:readonly && !gitlens:untrusted && !gitlens:hasVirtualFolders && gitlens:repos:withRemotes", "group": "1_gitlens_actions", "order": 3}, {"when": "webviewItem =~ /gitlens:upstreamStatus\\b/", "group": "1_gitlens_actions", "order": 3}]}}, "gitlens.graph.hideLocalBranch": {"label": "Hide Local Branch", "menus": {"webview/context": [{"when": "webviewItem =~ /gitlens:branch\\b(?!.*?\\b\\+(current|remote)\\b)/", "group": "8_gitlens_actions", "order": 11}]}}, "gitlens.graph.hideRefGroup": {"label": "<PERSON>de", "menus": {"webview/context": [{"when": "webviewItemGroup =~ /gitlens:refGroup\\b(?!.*?\\b\\+current\\b)/", "group": "8_gitlens_actions", "order": 12}]}}, "gitlens.graph.hideRemote": {"label": "Hide Remote", "menus": {"webview/context": [{"when": "webviewItem =~ /gitlens:branch\\b(?=.*?\\b\\+remote\\b)(?!.*?\\b\\+current\\b)/", "group": "8_gitlens_actions", "order": 10}]}}, "gitlens.graph.hideRemoteBranch": {"label": "Hide Remote Branch", "menus": {"webview/context": [{"when": "webviewItem =~ /gitlens:branch\\b(?=.*?\\b\\+remote\\b)(?!.*?\\b\\+current\\b)/", "group": "8_gitlens_actions", "order": 11}]}}, "gitlens.graph.hideTag": {"label": "Hide Tag", "menus": {"webview/context": [{"when": "webviewItem =~ /gitlens:tag\\b/", "group": "8_gitlens_actions", "order": 10}]}}, "gitlens.graph.mergeBranchInto": {"label": "Merge Branch into Current Branch...", "enablement": "!operationInProgress", "menus": {"webview/context": [{"when": "webviewItem =~ /gitlens:branch\\b(?!.*?\\b\\+current\\b)/ && !gitlens:readonly && !gitlens:untrusted && !gitlens:hasVirtualFolders", "group": "1_gitlens_actions_1", "order": 1}]}}, "gitlens.graph.openBranchOnRemote": {"label": "Open Branch on Remote", "icon": "$(globe)", "menus": {"webview/context": [{"when": "webviewItem =~ /gitlens:branch\\b(?=.*?\\b\\+(tracking|remote)\\b)/ && gitlens:repos:withRemotes", "group": "2_gitlens_quickopen", "order": 1}]}}, "gitlens.graph.openChangedFileDiffs": {"label": "Open All Changes", "icon": "$(diff-multiple)", "menus": {"gitlens/graph/commit/changes": [{"group": "1_gitlens", "order": 1}]}}, "gitlens.graph.openChangedFileDiffsIndividually": {"label": "Open All Changes Individually", "menus": {"gitlens/graph/commit/changes": [{"when": "config.gitlens.views.openChangesInMultiDiffEditor", "group": "1_gitlens", "order": 2}]}}, "gitlens.graph.openChangedFileDiffsWithMergeBase": {"label": "Open All Changes with Common Base", "icon": "$(diff-multiple)", "menus": {"webview/context": [{"when": "webviewItem =~ /gitlens:branch\\b(?!.*?\\b\\+current\\b)/", "group": "3_gitlens_explore", "order": 11}]}}, "gitlens.graph.openChangedFileDiffsWithWorking": {"label": "Open All Changes with <PERSON> Tree", "icon": "$(diff-multiple)", "menus": {"gitlens/graph/commit/changes": [{"when": "webviewItem != gitlens:wip", "group": "1_gitlens", "order": 3}]}}, "gitlens.graph.openChangedFileDiffsWithWorkingIndividually": {"label": "Open All Changes with Working Tree Individually", "menus": {"gitlens/graph/commit/changes": [{"when": "config.gitlens.views.openChangesInMultiDiffEditor", "group": "1_gitlens", "order": 4}]}}, "gitlens.graph.openChangedFileRevisions": {"label": "Open Files at Revision", "menus": {"gitlens/graph/commit/changes": [{"group": "2_git<PERSON>s", "order": 2}]}}, "gitlens.graph.openChangedFiles": {"label": "Open Files", "menus": {"gitlens/graph/commit/changes": [{"group": "2_git<PERSON>s", "order": 1}]}}, "gitlens.graph.openCommitOnRemote": {"label": "Open Commit on Remote", "icon": "$(globe)", "menus": {"webview/context": [{"when": "webviewItem =~ /gitlens:commit\\b/ && !listMultiSelection && gitlens:repos:withRemotes", "group": "3_gitlens_explore", "order": 2}]}}, "gitlens.graph.openCommitOnRemote.multi": {"label": "Open Commits on Remote", "icon": "$(globe)", "menus": {"webview/context": [{"when": "webviewItems =~ /gitlens:commit\\b/ && listMultiSelection && gitlens:repos:withRemotes", "group": "3_gitlens_explore", "order": 2}]}}, "gitlens.graph.openInWorktree": {"label": "Open in Worktree", "icon": "$(window)", "menus": {"webview/context": [{"when": "webviewItem =~ /gitlens:branch\\b(?!.*?\\b\\+(current|checkedout|worktree)\\b)/ && !gitlens:hasVirtualFolders", "group": "1_gitlens_action", "order": 3}]}}, "gitlens.graph.openOnlyChangedFiles": {"label": "Open Changed & Close Unchanged Files", "menus": {"gitlens/graph/commit/changes": [{"group": "2_git<PERSON>s", "order": 3}]}}, "gitlens.graph.openPullRequest": {"label": "Open Pull Request", "icon": "$(git-pull-request)", "menus": {"webview/context": [{"when": "webviewItem =~ /gitlens:pullrequest\\b/ && gitlens:action:openPullRequest > 1", "group": "1_gitlens_actions", "order": 98}]}}, "gitlens.graph.openPullRequestChanges": {"label": "Open Pull Request Changes", "icon": "$(diff-multiple)", "menus": {"webview/context": [{"when": "webviewItem =~ /gitlens:pullrequest\\b(?=.*?\\b\\+refs\\b)/", "group": "1_gitlens_actions", "order": 1}]}}, "gitlens.graph.openPullRequestComparison": {"label": "Compare Pull Request", "icon": "$(compare-changes)", "menus": {"webview/context": [{"when": "webviewItem =~ /gitlens:pullrequest\\b(?=.*?\\b\\+refs\\b)/", "group": "4_gitlens_compare", "order": 1}]}}, "gitlens.graph.openPullRequestOnRemote": {"label": "Open Pull Request on Remote", "icon": "$(globe)", "menus": {"webview/context": [{"when": "webviewItem =~ /gitlens:pullrequest\\b/", "group": "1_gitlens_actions", "order": 99}]}}, "gitlens.graph.openWorktree": {"label": "Open Worktree", "icon": "$(window)", "menus": {"webview/context": [{"when": "webviewItem =~ /gitlens:branch\\b(?=.*?\\b\\+worktree\\b)/ && !gitlens:hasVirtualFolders", "group": "1_gitlens_action", "order": 1}]}}, "gitlens.graph.openWorktreeInNewWindow": {"label": "Open Worktree in New Window", "icon": "$(empty-window)", "menus": {"webview/context": [{"when": "webviewItem =~ /gitlens:branch\\b(?=.*?\\b\\+worktree\\b)/ && !gitlens:hasVirtualFolders", "group": "1_gitlens_action", "order": 2}]}}, "gitlens.graph.publishBranch": {"label": "Publish Branch", "icon": "$(cloud-upload)", "enablement": "!operationInProgress", "menus": {"webview/context": [{"when": "webviewItem =~ /gitlens:branch\\b(?!.*?\\b\\+remote\\b)(?!.*?\\b\\+tracking\\b)(?!.*?\\b\\+closed\\b)/ && !gitlens:readonly && !gitlens:untrusted && !gitlens:hasVirtualFolders && gitlens:repos:withRemotes", "group": "1_gitlens_actions", "order": 2}]}}, "gitlens.graph.pull": {"label": "<PERSON><PERSON>", "icon": "$(repo-pull)", "enablement": "!operationInProgress", "menus": {"webview/context": [{"when": "webviewItem =~ /gitlens:branch\\b(?=.*?\\b\\+(behind|tracking)\\b)(?!.*?\\b\\+closed\\b)/ && !gitlens:readonly && !gitlens:untrusted && !gitlens:hasVirtualFolders && gitlens:repos:withRemotes", "group": "1_gitlens_actions", "order": 2}, {"when": "webviewItem =~ /gitlens:upstreamStatus\\b/", "group": "1_gitlens_actions", "order": 2}]}}, "gitlens.graph.push": {"label": "<PERSON><PERSON>", "icon": "$(repo-push)", "enablement": "!operationInProgress", "menus": {"webview/context": [{"when": "webviewItem =~ /gitlens:branch\\b(?=.*?\\b\\+ahead\\b)(?!.*?\\b\\+behind\\b)(?!.*?\\b\\+closed\\b)/ && !gitlens:readonly && !gitlens:untrusted && !gitlens:hasVirtualFolders && gitlens:repos:withRemotes", "group": "1_gitlens_actions", "order": 2}, {"when": "webviewItem =~ /gitlens:upstreamStatus\\b/", "group": "1_gitlens_actions", "order": 1}]}}, "gitlens.graph.pushWithForce": {"label": "Push (force)", "icon": "$(repo-force-push)", "enablement": "!operationInProgress"}, "gitlens.graph.rebaseOntoBranch": {"label": "Rebase Current Branch onto Branch...", "enablement": "!operationInProgress", "menus": {"webview/context": [{"when": "webviewItem =~ /gitlens:branch\\b(?!.*?\\b\\+current\\b)/ && !gitlens:readonly && !gitlens:untrusted && !gitlens:hasVirtualFolders", "group": "1_gitlens_actions_1", "order": 2}]}}, "gitlens.graph.rebaseOntoCommit": {"label": "Rebase Current Branch onto Commit...", "enablement": "!operationInProgress", "menus": {"webview/context": [{"when": "webviewItem =~ /gitlens:commit\\b/ && !listMultiSelection && !gitlens:readonly && !gitlens:untrusted && !gitlens:hasVirtualFolders", "group": "1_gitlens_actions", "order": 6}]}}, "gitlens.graph.rebaseOntoUpstream": {"label": "Rebase Current Branch onto Upstream...", "enablement": "!operationInProgress", "menus": {"webview/context": [{"when": "webviewItem =~ /gitlens:branch\\b(?=.*?\\b\\+current\\b)(?=.*?\\b\\+tracking\\b)/ && !gitlens:readonly && !gitlens:untrusted && !gitlens:hasVirtualFolders", "group": "1_gitlens_actions_1", "order": 2}]}}, "gitlens.graph.refresh": {"label": "Refresh", "icon": "$(refresh)", "menus": {"editor/title": [{"when": "activeWebviewPanelId === gitlens.graph", "group": "navigation", "order": -99}]}}, "gitlens.graph.renameBranch": {"label": "Rename Branch...", "enablement": "!operationInProgress", "menus": {"webview/context": [{"when": "webviewItem =~ /gitlens:branch\\b/ && !gitlens:readonly && !gitlens:untrusted && !gitlens:hasVirtualFolders", "group": "1_gitlens_actions_2", "order": 2}]}}, "gitlens.graph.resetColumnsCompact": {"label": "Reset Columns to Compact Layout", "menus": {"webview/context": [{"when": "webviewItem =~ /gitlens:graph:(columns|settings)\\b/", "group": "3_columns", "order": 2}]}}, "gitlens.graph.resetColumnsDefault": {"label": "Reset Columns to Default Layout", "menus": {"webview/context": [{"when": "webviewItem =~ /gitlens:graph:(columns|settings)\\b/", "group": "3_columns", "order": 1}]}}, "gitlens.graph.resetCommit": {"label": "Reset Current Branch to Previous Commit...", "enablement": "!operationInProgress", "menus": {"webview/context": [{"when": "webviewItem =~ /gitlens:commit\\b/ && !listMultiSelection && !gitlens:readonly && !gitlens:untrusted && !gitlens:hasVirtualFolders", "group": "1_gitlens_actions", "order": 5}]}}, "gitlens.graph.resetToCommit": {"label": "Reset Current Branch to Commit...", "enablement": "!operationInProgress", "menus": {"webview/context": [{"when": "webviewItem =~ /gitlens:commit\\b/ && !listMultiSelection && !gitlens:readonly && !gitlens:untrusted && !gitlens:hasVirtualFolders", "group": "1_gitlens_actions", "order": 4}]}}, "gitlens.graph.resetToTag": {"label": "Reset Current Branch to Tag...", "icon": "$(gitlens-reset)", "enablement": "!operationInProgress", "menus": {"webview/context": [{"when": "webviewItem =~ /gitlens:tag\\b/ && !listMultiSelection && !gitlens:readonly && !gitlens:untrusted && !gitlens:hasVirtualFolders", "group": "1_gitlens_actions", "order": 2}]}}, "gitlens.graph.resetToTip": {"label": "Reset Current Branch to Tip...", "enablement": "!operationInProgress", "menus": {"webview/context": [{"when": "webviewItem =~ /gitlens:branch\\b/ && !gitlens:readonly && !gitlens:untrusted && !gitlens:hasVirtualFolders", "group": "1_gitlens_actions_1", "order": 3}]}}, "gitlens.graph.revert": {"label": "Revert Commit...", "enablement": "!operationInProgress", "menus": {"webview/context": [{"when": "webviewItem =~ /gitlens:commit\\b(?=.*?\\b\\+current\\b)/ && !listMultiSelection && !gitlens:readonly && !gitlens:untrusted && !gitlens:hasVirtualFolders", "group": "1_gitlens_actions", "order": 3}]}}, "gitlens.graph.scrollMarkerLocalBranchOff": {"label": "Hide Local Branch Markers", "menus": {"gitlens/graph/markers": [{"when": "webviewItem =~ /gitlens:graph:settings\\b/ && webviewItemValue =~ /\\bscrollMarker:localBranches:enabled\\b/", "group": "4_settings", "order": 1}]}}, "gitlens.graph.scrollMarkerLocalBranchOn": {"label": "Show Local Branch Markers", "menus": {"gitlens/graph/markers": [{"when": "webviewItem =~ /gitlens:graph:settings\\b/ && webviewItemValue =~ /\\bscrollMarker:localBranches:disabled\\b/", "group": "4_settings", "order": 1}]}}, "gitlens.graph.scrollMarkerPullRequestOff": {"label": "<PERSON><PERSON> Pull Request Markers", "menus": {"gitlens/graph/markers": [{"when": "webviewItem =~ /gitlens:graph:settings\\b/ && webviewItemValue =~ /\\bscrollMarker:pullRequests:enabled\\b/", "group": "4_settings", "order": 3}]}}, "gitlens.graph.scrollMarkerPullRequestOn": {"label": "Show Pull Request Markers", "menus": {"gitlens/graph/markers": [{"when": "webviewItem =~ /gitlens:graph:settings\\b/ && webviewItemValue =~ /\\bscrollMarker:pullRequests:disabled\\b/", "group": "4_settings", "order": 3}]}}, "gitlens.graph.scrollMarkerRemoteBranchOff": {"label": "Hide Remote Branch Markers", "menus": {"gitlens/graph/markers": [{"when": "webviewItem =~ /gitlens:graph:settings\\b/ && webviewItemValue =~ /\\bscrollMarker:remoteBranches:enabled\\b/", "group": "4_settings", "order": 2}]}}, "gitlens.graph.scrollMarkerRemoteBranchOn": {"label": "Show Remote Branch Markers", "menus": {"gitlens/graph/markers": [{"when": "webviewItem =~ /gitlens:graph:settings\\b/ && webviewItemValue =~ /\\bscrollMarker:remoteBranches:disabled\\b/", "group": "4_settings", "order": 2}]}}, "gitlens.graph.scrollMarkerStashOff": {"label": "Hide Stash Markers", "menus": {"gitlens/graph/markers": [{"when": "webviewItem =~ /gitlens:graph:settings\\b/ && webviewItemValue =~ /\\bscrollMarker:stashes:enabled\\b/", "group": "4_settings", "order": 4}]}}, "gitlens.graph.scrollMarkerStashOn": {"label": "Show Stash Markers", "menus": {"gitlens/graph/markers": [{"when": "webviewItem =~ /gitlens:graph:settings\\b/ && webviewItemValue =~ /\\bscrollMarker:stashes:disabled\\b/", "group": "4_settings", "order": 4}]}}, "gitlens.graph.scrollMarkerTagOff": {"label": "Hide Tag Markers", "menus": {"gitlens/graph/markers": [{"when": "webviewItem =~ /gitlens:graph:settings\\b/ && webviewItemValue =~ /\\bscrollMarker:tags:enabled\\b/", "group": "4_settings", "order": 5}]}}, "gitlens.graph.scrollMarkerTagOn": {"label": "Show Tag Markers", "menus": {"gitlens/graph/markers": [{"when": "webviewItem =~ /gitlens:graph:settings\\b/ && webviewItemValue =~ /\\bscrollMarker:tags:disabled\\b/", "group": "4_settings", "order": 5}]}}, "gitlens.graph.shareAsCloudPatch": {"label": "Share as Cloud Patch...", "menus": {"gitlens/share": [{"when": "webviewItem =~ /gitlens:(commit|stash)\\b/ && !listMultiSelection && !gitlens:untrusted && !gitlens:hasVirtualFolders && gitlens:gk:organization:drafts:enabled && config.gitlens.cloudPatches.enabled", "group": "1_a_gitlens", "order": 1}]}}, "gitlens.graph.showInDetailsView": {"label": "Inspect Details", "icon": "$(eye)", "menus": {"webview/context": [{"when": "webviewItem =~ /gitlens:(commit|stash|wip)\\b/ && !listMultiSelection", "group": "3_gitlens_explore", "order": 0}]}}, "gitlens.graph.split": {"label": "Split Commit Graph", "icon": "$(split-horizontal)", "commandPalette": "gitlens:enabled && config.gitlens.graph.allowMultiple", "menus": {"editor/title": [{"when": "activeWebviewPanelId == gitlens.graph && resourceScheme == webview-panel && config.gitlens.graph.allowMultiple", "group": "navigation", "order": -97}], "editor/title/context": [{"when": "activeWebviewPanelId == gitlens.graph && resourceScheme == webview-panel && config.gitlens.graph.allowMultiple", "group": "6_split_in_group_gitlens", "order": 2}]}}, "gitlens.graph.stash.apply": {"label": "Apply Stash...", "icon": "$(gitlens-stash-pop)", "enablement": "!operationInProgress", "menus": {"webview/context": [{"when": "webviewItem == gitlens:stash && !listMultiSelection && !gitlens:readonly && !gitlens:untrusted && !gitlens:hasVirtualFolders", "group": "1_gitlens_actions", "order": 1}]}}, "gitlens.graph.stash.delete": {"label": "Drop Stash...", "icon": "$(trash)", "enablement": "!operationInProgress", "menus": {"webview/context": [{"when": "webviewItem == gitlens:stash && !listMultiSelection && !gitlens:readonly && !gitlens:untrusted && !gitlens:hasVirtualFolders", "group": "1_gitlens_actions", "order": 3}]}}, "gitlens.graph.stash.rename": {"label": "<PERSON><PERSON>...", "icon": "$(edit)", "enablement": "!operationInProgress", "menus": {"webview/context": [{"when": "webviewItem == gitlens:stash && !listMultiSelection && !gitlens:readonly && !gitlens:untrusted && !gitlens:hasVirtualFolders", "group": "1_gitlens_actions", "order": 2}]}}, "gitlens.graph.stash.save": {"label": "Stash All Changes...", "icon": "$(gitlens-stash-save)", "enablement": "!operationInProgress", "menus": {"webview/context": [{"when": "webviewItem == gitlens:wip && !listMultiSelection && !gitlens:readonly && !gitlens:untrusted && !gitlens:hasVirtualFolders", "group": "1_gitlens_actions", "order": 3}]}}, "gitlens.graph.switchToAnotherBranch": {"label": "Switch to Another Branch...", "icon": "$(gitlens-switch)", "enablement": "!operationInProgress", "menus": {"webview/context": [{"when": "webviewItem =~ /gitlens:branch\\b(?=.*?\\b\\+(current|checkedout)\\b)/ && !gitlens:readonly && !gitlens:untrusted && !gitlens:hasVirtualFolders", "group": "1_gitlens_action", "order": 1}]}}, "gitlens.graph.switchToBranch": {"label": "Switch to Branch...", "icon": "$(gitlens-switch)", "enablement": "!operationInProgress", "menus": {"webview/context": [{"when": "webviewItem =~ /gitlens:branch\\b(?!.*?\\b\\+(current|checkedout|worktree)\\b)/ && !gitlens:readonly && !gitlens:untrusted && !gitlens:hasVirtualFolders", "group": "1_gitlens_action", "order": 1}]}}, "gitlens.graph.switchToCommit": {"label": "Switch to Commit...", "icon": "$(gitlens-switch)", "enablement": "!operationInProgress", "menus": {"webview/context": [{"when": "webviewItem =~ /gitlens:commit\\b/ && !listMultiSelection && !gitlens:readonly && !gitlens:untrusted && !gitlens:hasVirtualFolders", "group": "1_gitlens_actions", "order": 7}]}}, "gitlens.graph.switchToEditorLayout": {"label": "Prefer Commit Graph in Editor", "enablement": "config.gitlens.graph.layout != editor", "commandPalette": "gitlens:enabled && config.gitlens.graph.layout != editor", "menus": {"gitlens/graph/configuration": [{"group": "1_gitlens", "order": 1}]}}, "gitlens.graph.switchToPanelLayout": {"label": "Prefer Commit Graph in Panel", "enablement": "config.gitlens.graph.layout != panel", "commandPalette": "gitlens:enabled && config.gitlens.graph.layout != panel", "menus": {"gitlens/graph/configuration": [{"group": "1_gitlens", "order": 1}]}}, "gitlens.graph.switchToTag": {"label": "Switch to Tag...", "icon": "$(gitlens-switch)", "enablement": "!operationInProgress", "menus": {"webview/context": [{"when": "webviewItem =~ /gitlens:tag\\b/ && !gitlens:readonly && !gitlens:untrusted && !gitlens:hasVirtualFolders", "group": "1_gitlens_actions", "order": 1}]}}, "gitlens.graph.undoCommit": {"label": "Undo Commit", "icon": "$(discard)", "enablement": "!operationInProgress", "menus": {"webview/context": [{"when": "webviewItem =~ /gitlens:commit\\b(?=.*?\\b\\+HEAD\\b)/ && !listMultiSelection && !gitlens:readonly && !gitlens:untrusted && !gitlens:hasVirtualFolders", "group": "1_gitlens_actions", "order": 1}]}}, "gitlens.inviteToLiveShare": {"label": "Invite to Live Share", "icon": "$(live-share)", "menus": {"view/item/context": [{"when": "viewItem =~ /gitlens:contributor\\b(?!.*?\\b\\+current\\b)/ && gitlens:vsls && gitlens:vsls != guest", "group": "inline", "order": 1}, {"when": "viewItem =~ /gitlens:contributor\\b(?!.*?\\b\\+current\\b)/ && !listMultiSelection && gitlens:vsls && gitlens:vsls != guest", "group": "1_gitlens_actions", "order": 1}]}}, "gitlens.launchpad.indicator.toggle": {"label": "Toggle Launchpad Indicator", "icon": "$(rocket)", "commandPalette": "gitlens:enabled"}, "gitlens.openAssociatedPullRequestOnRemote": {"label": "Open Associated Pull Request", "icon": "$(git-pull-request)", "commandPalette": "gitlens:repos:withRemotes"}, "gitlens.openBlamePriorToChange": {"label": "Open Blame Prior to Change", "icon": "$(versions)", "commandPalette": "resource in gitlens:tabs:tracked"}, "gitlens.openBranchesOnRemote": {"label": "Open Branches on Remote", "icon": "$(globe)", "commandPalette": "gitlens:repos:withRemotes", "menus": {"view/item/context": [{"when": "viewItem =~ /gitlens:branches\\b(?=.*?\\b\\+remotes\\b)/", "group": "inline", "order": 98, "alt": "gitlens.copyRemoteBranchesUrl"}, {"when": "viewItem =~ /gitlens:branches\\b(?=.*?\\b\\+remotes\\b)/ && !listMultiSelection", "group": "2_gitlens_quickopen", "order": 1}, {"when": "viewItem =~ /gitlens:remote\\b/ && !listMultiSelection", "group": "5_gitlens_open", "order": 2}]}}, "gitlens.openBranchOnRemote": {"label": "Open Branch on Remote", "icon": "$(globe)", "commandPalette": "gitlens:repos:withRemotes"}, "gitlens.openChangedFiles": {"label": "Open Changed Files", "commandPalette": "gitlens:enabled", "menus": {"scm/resourceGroup/context": [{"when": "scmResourceGroup =~ /^(workingTree|index)$/ && scmProvider == git && gitlens:enabled && config.gitlens.menus.scmGroup.openClose", "group": "3_git<PERSON>s", "order": 1}]}}, "gitlens.openCloudPatch": {"label": "Open Cloud Patch..."}, "gitlens.openCommitOnRemote": {"label": "Open Commit on Remote", "icon": "$(globe)", "commandPalette": "gitlens:repos:withRemotes", "menus": {"gitlens/editor/context/openOn": [{"group": "1_gitlens_commit", "order": 1, "alt": "gitlens.copyRemoteCommitUrl"}], "gitlens/editor/lineNumber/context/openOn": [{"group": "1_gitlens_commit", "order": 1, "alt": "gitlens.copyRemoteCommitUrl"}], "timeline/item/context": [{"when": "false && gitlens:enabled && gitlens:repos:withRemotes && timelineItem =~ /git:file:commit\\b/", "group": "inline", "order": 99, "alt": "gitlens.copyRemoteCommitUrl"}]}}, "gitlens.openComparisonOnRemote": {"label": "Open Comparison on Remote", "icon": "$(globe)", "menus": {"view/item/context": [{"when": "viewItem =~ /gitlens:compare:(branch\\b(?=.*?\\b\\+comparing\\b)|results(?!:))/ && !listMultiSelection && gitlens:repos:withRemotes", "group": "2_gitlens_quickopen", "order": 1}, {"when": "viewItem =~ /gitlens:compare:results:commits\\b/ && gitlens:repos:withRemotes", "group": "inline", "order": 99, "alt": "gitlens.copyRemoteComparisonUrl"}, {"when": "viewItem =~ /gitlens:compare:results:commits\\b/ && !listMultiSelection && gitlens:repos:withRemotes", "group": "3_gitlens_explore", "order": 0}]}}, "gitlens.openCurrentBranchOnRemote": {"label": "Open Current Branch on Remote", "icon": "$(globe)", "commandPalette": "gitlens:repos:withRemotes"}, "gitlens.openFileFromRemote": {"label": "Open File from Remote", "commandPalette": "gitlens:enabled"}, "gitlens.openFileHistory": {"label": "Open File History", "commandPalette": "resource in gitlens:tabs:tracked", "menus": {"gitlens/commit/file/history": [{"when": "view != gitlens.views.fileHistory/", "group": "1_gitlens", "order": 1}], "gitlens/editor/history": [{"group": "1_gitlens", "order": 1}], "gitlens/explorer/file/history": [{"group": "1_gitlens", "order": 1}], "gitlens/scm/resourceState/history": [{"group": "1_gitlens", "order": 1}]}}, "gitlens.openFileOnRemote": {"label": "Open File on Remote", "icon": "$(globe)", "commandPalette": "gitlens:repos:withRemotes", "menus": {"gitlens/editor/context/openOn": [{"group": "1_gitlens", "order": 2, "alt": "gitlens.copyRemoteFileUrlToClipboard"}], "gitlens/editor/lineNumber/context/openOn": [{"group": "1_gitlens", "order": 2, "alt": "gitlens.copyRemoteFileUrlToClipboard"}], "gitlens/editor/openOn": [{"group": "1_gitlens", "order": 1, "alt": "gitlens.copyRemoteFileUrlWithoutRange"}], "gitlens/explorer/openOn": [{"group": "1_gitlens", "order": 1, "alt": "gitlens.copyRemoteFileUrlWithoutRange"}], "gitlens/scm/resourceState/openOn": [{"group": "1_gitlens", "order": 1, "alt": "gitlens.copyRemoteFileUrlWithoutRange"}], "view/item/context": [{"when": "viewItem =~ /gitlens:file\\b((?=.*?\\b\\+committed\\b)|:results)/ && gitlens:repos:withRemotes", "group": "inline", "order": 99, "alt": "gitlens.copyRemoteFileUrlWithoutRange"}, {"when": "viewItem =~ /gitlens:file\\b((?=.*?\\b\\+committed\\b)|:results)/ && !listMultiSelection && gitlens:repos:withRemotes", "group": "2_gitlens_quickopen_file", "order": 5, "alt": "gitlens.copyRemoteFileUrlWithoutRange"}, {"when": "viewItem =~ /gitlens:(history:(file|line)|status:file)\\b/ && !listMultiSelection && gitlens:repos:withRemotes", "group": "5_gitlens_open", "order": 2, "alt": "gitlens.copyRemoteFileUrlWithoutRange"}]}}, "gitlens.openFileOnRemoteFrom": {"label": "Open File on Remote From...", "icon": "$(globe)", "commandPalette": "gitlens:repos:withRemotes", "menus": {"gitlens/editor/context/openOn": [{"group": "1_gitlens", "order": 3, "alt": "gitlens.copyRemoteFileUrlFrom"}], "gitlens/editor/lineNumber/context/openOn": [{"group": "1_gitlens", "order": 3, "alt": "gitlens.copyRemoteFileUrlFrom"}], "gitlens/editor/openOn": [{"group": "1_gitlens", "order": 2, "alt": "gitlens.copyRemoteFileUrlFrom"}], "gitlens/explorer/openOn": [{"group": "1_gitlens", "order": 2, "alt": "gitlens.copyRemoteFileUrlFrom"}], "gitlens/scm/resourceState/openOn": [{"group": "1_gitlens", "order": 2, "alt": "gitlens.copyRemoteFileUrlFrom"}]}}, "gitlens.openFileRevision": {"label": "Open File at Revision...", "icon": "$(gitlens-open-revision)", "commandPalette": "resource in gitlens:tabs:tracked"}, "gitlens.openFileRevisionFrom": {"label": "Open File at Revision from...", "icon": "$(gitlens-open-revision)", "commandPalette": "resource in gitlens:tabs:tracked"}, "gitlens.openFolderHistory": {"label": "Open Folder History", "menus": {"gitlens/explorer/folder/history": [{"group": "1_gitlens", "order": 1}], "gitlens/scm/resourceFolder/history": [{"group": "1_gitlens", "order": 2}]}}, "gitlens.openOnlyChangedFiles": {"label": "Open Changed & Close Unchanged Files", "commandPalette": "gitlens:enabled", "menus": {"scm/resourceGroup/context": [{"when": "scmResourceGroup =~ /^(workingTree|index)$/ && scmProvider == git && gitlens:enabled && config.gitlens.menus.scmGroup.openClose", "group": "3_git<PERSON>s", "order": 3}]}}, "gitlens.openPatch": {"label": "Open Patch...", "commandPalette": "false && gitlens:enabled", "menus": {"editor/title": [{"when": "false && editorLangId == diff"}]}}, "gitlens.openPullRequestOnRemote": {"label": "Open Pull Request on Remote", "icon": "$(globe)", "menus": {"view/item/context": [{"when": "viewItem =~ /gitlens:(pullrequest\\b|launchpad:item\\b(?=.*?\\b\\+pr\\b))/", "group": "inline", "order": 98, "alt": "gitlens.copyRemotePullRequestUrl"}, {"when": "viewItem =~ /gitlens:(pullrequest\\b|launchpad:item\\b(?=.*?\\b\\+pr\\b))/ && !listMultiSelection", "group": "2_gitlens_quickopen", "order": 2}]}}, "gitlens.openRepoOnRemote": {"label": "Open Repository on Remote", "icon": "$(globe)", "commandPalette": "gitlens:repos:withRemotes", "menus": {"view/item/context": [{"when": "viewItem =~ /gitlens:remote\\b/", "group": "inline", "order": 99, "alt": "gitlens.copyRemoteRepositoryUrl"}, {"when": "viewItem =~ /gitlens:remote\\b/ && !listMultiSelection", "group": "5_gitlens_open", "order": 1}, {"when": "viewItem =~ /gitlens:repo-folder\\b/ && !listMultiSelection && gitlens:repos:withRemotes", "group": "2_gitlens_quickopen", "order": 3}, {"when": "viewItem =~ /gitlens:repository\\b/ && !listMultiSelection && gitlens:repos:withRemotes", "group": "2_gitlens_quickopen", "order": 4}]}}, "gitlens.openRevisionFile": {"label": "Open File at Revision", "icon": "$(gitlens-open-revision)", "enablement": "gitlens:enabled && resourceScheme =~ /^(gitlens|pr)$/", "commandPalette": "gitlens:enabled && resourceScheme =~ /^(gitlens|git|pr)$/ && isInDiffEditor", "menus": {"editor/title": [{"when": "resourceScheme =~ /^(gitlens|pr)$/ && gitlens:enabled && isInDiffEditor", "group": "navigation", "order": -97}]}}, "gitlens.openRevisionFromRemote": {"label": "Open File at Revision from Remote", "commandPalette": "gitlens:enabled"}, "gitlens.openWorkingFile": {"label": "Open File", "icon": "$(go-to-file)", "commandPalette": "gitlens:enabled && resourceScheme =~ /^(gitlens|git|pr)$/", "menus": {"editor/context": [{"when": "resourceScheme == gitlens && editorTextFocus && config.gitlens.menus.editor.compare", "group": "1_z_gitlens", "order": 0}], "editor/title": [{"when": "resourceScheme == git && gitlens:enabled && !isInDiffEditor", "group": "navigation", "order": -98}, {"when": "resourceScheme =~ /^(gitlens|pr)$/ && gitlens:enabled", "group": "navigation", "order": -98}], "editor/title/context": [{"when": "resourceScheme == gitlens && resourceScheme in gitlens:schemes:trackable", "group": "2_a_gitlens", "order": 0}]}}, "gitlens.pastePatchFromClipboard": {"label": "Paste Copied Changes (Patch)", "commandPalette": "gitlens:enabled && !gitlens:untrusted && !gitlens:hasVirtualFolders"}, "gitlens.plus.cloudIntegrations.connect": {"label": "Connect Integrations..."}, "gitlens.plus.cloudIntegrations.manage": {"label": "Manage Integrations...", "commandPalette": "gitlens:plus"}, "gitlens.plus.hide": {"label": "Hide Pro Features", "commandPalette": "config.gitlens.plusFeatures.enabled"}, "gitlens.plus.login": {"label": "Sign In to GitKraken...", "commandPalette": "!gitlens:plus", "menus": {"view/item/context": [{"when": "viewItem == gitlens:message:signin", "group": "inline", "order": 1}]}}, "gitlens.plus.logout": {"label": "Sign Out of GitKraken", "commandPalette": true}, "gitlens.plus.manage": {"label": "Manage Your Account...", "commandPalette": "gitlens:plus"}, "gitlens.plus.reactivateProTrial": {"label": "Reactivate Pro Trial", "commandPalette": "gitlens:plus:state == 5"}, "gitlens.plus.referFriend": {"label": "Refer a friend", "commandPalette": "gitlens:plus:state == 6"}, "gitlens.plus.refreshRepositoryAccess": {"label": "Refresh Repository Access", "commandPalette": "gitlens:enabled"}, "gitlens.plus.restore": {"label": "Restore Pro Features", "commandPalette": "!config.gitlens.plusFeatures.enabled"}, "gitlens.plus.signUp": {"label": "Sign Up for GitKraken...", "commandPalette": "!gitlens:plus"}, "gitlens.plus.simulateSubscription": {"label": "Simulate Subscription (Debugging)", "commandPalette": "gitlens:enabled && gitlens:debugging"}, "gitlens.plus.upgrade": {"label": "Upgrade to Pro...", "commandPalette": true}, "gitlens.pullRepositories": {"label": "<PERSON><PERSON>", "icon": "$(repo-pull)", "enablement": "!operationInProgress", "commandPalette": "gitlens:repos:withRemotes && !gitlens:readonly && !gitlens:untrusted && !gitlens:hasVirtualFolders", "menus": {"gitlens/views/grouped/commits": [{"when": "gitlens:views:scm:grouped:view == commits && !gitlens:readonly && !gitlens:untrusted && !gitlens:hasVirtualFolders && gitlens:repos:withRemotes", "group": "2_gitlens_actions", "order": 2}], "gitlens/views/grouped/repositories": [{"when": "gitlens:views:scm:grouped:view == repositories && !gitlens:readonly && !gitlens:untrusted && !gitlens:hasVirtualFolders && gitlens:repos:withRemotes", "group": "2_gitlens_actions", "order": 2}], "view/title": [{"when": "view == gitlens.views.commits && !gitlens:readonly && !gitlens:untrusted && !gitlens:hasVirtualFolders && gitlens:repos:withRemotes", "group": "navigation", "order": 2}, {"when": "view == gitlens.views.repositories && !gitlens:readonly && !gitlens:untrusted && !gitlens:hasVirtualFolders && gitlens:repos:withRemotes", "group": "navigation", "order": 2}]}}, "gitlens.pushRepositories": {"label": "<PERSON><PERSON>", "icon": "$(repo-push)", "enablement": "!operationInProgress", "commandPalette": "gitlens:repos:withRemotes && !gitlens:readonly && !gitlens:untrusted && !gitlens:hasVirtualFolders", "menus": {"gitlens/views/grouped/commits": [{"when": "gitlens:views:scm:grouped:view == commits && !gitlens:readonly && !gitlens:untrusted && !gitlens:hasVirtualFolders && gitlens:repos:withRemotes", "group": "2_gitlens_actions", "order": 1}], "gitlens/views/grouped/repositories": [{"when": "gitlens:views:scm:grouped:view == repositories && !gitlens:readonly && !gitlens:untrusted && !gitlens:hasVirtualFolders && gitlens:repos:withRemotes", "group": "2_gitlens_actions", "order": 1}], "view/title": [{"when": "view == gitlens.views.commits && !gitlens:readonly && !gitlens:untrusted && !gitlens:hasVirtualFolders && gitlens:repos:withRemotes", "group": "navigation", "order": 1}, {"when": "view == gitlens.views.repositories && !gitlens:readonly && !gitlens:untrusted && !gitlens:hasVirtualFolders && gitlens:repos:withRemotes", "group": "navigation", "order": 1}]}}, "gitlens.quickOpenFileHistory": {"label": "Quick Open File History", "commandPalette": "resource in gitlens:tabs:tracked", "menus": {"gitlens/commit/file/history": [{"group": "1_gitlens_quick", "order": 1}], "gitlens/editor/history": [{"group": "1_gitlens_quick", "order": 1}], "gitlens/explorer/file/history": [{"group": "1_gitlens_quick", "order": 1}], "gitlens/scm/resourceState/history": [{"group": "1_gitlens_quick", "order": 1}]}}, "gitlens.reset": {"label": "Reset Stored Data...", "commandPalette": true}, "gitlens.resetViewsLayout": {"label": "Reset Views Layout", "commandPalette": true}, "gitlens.revealCommitInView": {"label": "Reveal Commit in Side Bar", "commandPalette": "resource in gitlens:tabs:blameable", "menus": {"gitlens/commit/file/commit": [{"when": "viewItem =~ /gitlens:file\\b(?=.*?\\b\\+committed\\b)/ && view =~ /gitlens\\.views\\.(fileHistory|lineHistory\\b)/", "group": "navigation", "order": 4}], "view/item/context": [{"when": "viewItem =~ /gitlens:commit\\b/ && view =~ /gitlens\\.views\\.(?!commits|branches\\b)/ && !listMultiSelection", "group": "3_gitlens_explore", "order": 2}]}}, "gitlens.shareAsCloudPatch": {"label": "Share as Cloud Patch...", "commandPalette": "gitlens:enabled && gitlens:gk:organization:drafts:enabled && config.gitlens.cloudPatches.enabled", "menus": {"git.commit": [{"when": "gitlens:enabled && scmProvider == git && config.gitlens.menus.scmRepository.patch", "group": "4_git<PERSON>s", "order": 5}], "gitlens/share": [{"when": "viewItem =~ /gitlens:(commit|file\\b(?=.*?\\b\\+committed\\b)|stash|compare:results(?!:))\\b/ && !listMultiSelection && !gitlens:untrusted && !gitlens:hasVirtualFolders && gitlens:gk:organization:drafts:enabled && config.gitlens.cloudPatches.enabled", "group": "1_a_gitlens", "order": 1}], "scm/resourceGroup/context": [{"when": "scmResourceGroup =~ /^(workingTree|index)$/ && scmProvider == git && gitlens:enabled && config.gitlens.menus.scmGroup.patch", "group": "7_cutcopypaste", "order": 98}], "scm/title": [{"when": "scmProvider == git && gitlens:enabled && config.gitlens.menus.scmRepository.patch", "group": "2_z_gitlens", "order": 3}]}}, "gitlens.showAccountView": {"label": "Show Account on Home", "commandPalette": true}, "gitlens.showBranchesView": {"label": "Show Branches View", "commandPalette": "gitlens:enabled"}, "gitlens.showCommitDetailsView": {"label": "Show Inspect View", "commandPalette": "gitlens:enabled"}, "gitlens.showCommitInView": {"label": "Inspect Commit <PERSON>", "icon": "$(eye)", "commandPalette": "resource in gitlens:tabs:blameable"}, "gitlens.showCommitSearch": {"label": "Search Commits", "icon": "$(search)", "commandPalette": "gitlens:enabled", "menus": {"view/item/context": [{"when": "viewItem =~ /gitlens:repo-folder\\b/ && !listMultiSelection", "group": "3_gitlens_explore", "order": 2}, {"when": "viewItem =~ /gitlens:repository\\b/ && !listMultiSelection", "group": "3_gitlens_explore", "order": 1}]}}, "gitlens.showCommitsInView": {"label": "Search for Commits within Selection", "commandPalette": "resource in gitlens:tabs:blameable", "menus": {"gitlens/editor/context/changes": [{"when": "editorTextFocus && editorHasSelection", "group": "3_git<PERSON>s", "order": 2}]}}, "gitlens.showCommitsView": {"label": "Show Commits View", "commandPalette": "gitlens:enabled"}, "gitlens.showContributorsView": {"label": "Show Contributors View", "commandPalette": "gitlens:enabled"}, "gitlens.showDraftsView": {"label": "Show Cloud Patches View", "commandPalette": "gitlens:enabled && gitlens:gk:organization:drafts:enabled"}, "gitlens.showFileHistoryView": {"label": "Show File History View", "commandPalette": "gitlens:enabled"}, "gitlens.showGraph": {"label": "Show Commit Graph", "icon": "$(gitlens-graph)", "commandPalette": "gitlens:enabled", "menus": {"gitlens/views/grouped/commits": [{"when": "gitlens:views:scm:grouped:view == commits && !gitlens:plus:disabled", "group": "8_gitlens_toggles", "order": 0}], "scm/sourceControl": [{"when": "scmProvider == git && gitlens:enabled && config.gitlens.menus.scm.graph && !gitlens:plus:disabled && scmProviderRootUri not in gitlens:plus:disallowedRepos", "group": "6_git<PERSON>s", "order": 1}], "scm/title": [{"when": "scmProvider == git && gitlens:enabled && !gitlens:plus:disabled && scmProviderRootUri not in gitlens:plus:disallowedRepos && config.gitlens.menus.scmRepositoryInline.graph", "group": "navigation", "order": -1}, {"when": "scmProvider == git && gitlens:enabled && !gitlens:plus:disabled && scmProviderRootUri not in gitlens:plus:disallowedRepos && config.gitlens.menus.scmRepository.graph", "group": "2_z_gitlens", "order": 5}], "view/item/context": [{"when": "viewItem =~ /gitlens:repo-folder\\b/ && !gitlens:plus:disabled", "group": "inline", "order": 100}, {"when": "viewItem =~ /gitlens:repo-folder\\b/ && !listMultiSelection && !gitlens:plus:disabled", "group": "3_gitlens_explore", "order": 1}], "view/title": [{"when": "view == gitlens.views.commits && !gitlens:plus:disabled", "group": "navigation", "order": 11}, {"when": "view == gitlens.views.commits && !gitlens:plus:disabled", "group": "8_gitlens_toggles", "order": 0}]}}, "gitlens.showGraphPage": {"label": "Show Commit Graph in Editor", "icon": "$(gitlens-graph)", "commandPalette": "gitlens:enabled"}, "gitlens.showGraphView": {"label": "Show Commit Graph View", "icon": "$(gitlens-graph)", "commandPalette": "gitlens:enabled"}, "gitlens.showHomeView": {"label": "Show Home View", "commandPalette": true}, "gitlens.showInCommitGraph": {"label": "Open in Commit Graph", "icon": "$(gitlens-graph)", "menus": {"gitlens/commit/file/commit": [{"when": "viewItem =~ /gitlens:file\\b(?=.*?\\b\\+committed\\b)\\b/ && view =~ /gitlens\\.views\\.(fileHistory|lineHistory\\b)/", "group": "navigation", "order": 2}], "view/item/context": [{"when": "viewItem =~ /gitlens:(branch|tag)\\b(?!.*?\\b\\+closed\\b)/ && !listMultiSelection", "group": "1_gitlens_action", "order": 100}, {"when": "viewItem =~ /gitlens:(commit|stash)\\b(?!.*?\\b\\+closed\\b)/ && !listMultiSelection", "group": "3_gitlens_explore", "order": 1}, {"when": "viewItem =~ /gitlens:pullrequest\\b(?=.*?\\b\\+refs\\b)/ && !listMultiSelection", "group": "3_gitlens_explore", "order": 1}]}}, "gitlens.showInCommitGraphView": {"label": "Open in Commit Graph", "icon": "$(gitlens-graph)"}, "gitlens.showInDetailsView": {"label": "Inspect Details", "icon": "$(eye)", "menus": {"gitlens/commit/file/commit": [{"when": "viewItem =~ /gitlens:file\\b(?=.*?\\b\\+committed\\b)/ && view =~ /gitlens\\.views\\.(fileHistory|lineHistory\\b)/", "group": "navigation", "order": 1}], "view/item/context": [{"when": "viewItem =~ /gitlens:(commit|stash)\\b/ && !listMultiSelection", "group": "3_gitlens_explore", "order": 0}, {"when": "viewItem =~ /gitlens:file\\b(?=.*?\\b\\+committed\\b)/ && view =~ /gitlens\\.views\\.(fileHistory|lineHistory\\b)/ && !listMultiSelection", "group": "2_gitlens_quickopen", "order": 3}]}}, "gitlens.showLastQuickPick": {"label": "Show Last Opened Quick Pick", "commandPalette": "gitlens:enabled"}, "gitlens.showLaunchpad": {"label": "Open Launchpad", "icon": "$(rocket)", "commandPalette": "gitlens:enabled"}, "gitlens.showLaunchpadView": {"label": "Show Launchpad View", "icon": "$(rocket)", "commandPalette": "gitlens:enabled"}, "gitlens.showLineCommitInView": {"label": "Inspect Line Commit Details", "icon": "$(eye)", "commandPalette": "resource in gitlens:tabs:blameable", "menus": {"gitlens/editor/context/changes": [{"group": "3_git<PERSON>s", "order": 2}], "gitlens/editor/lineNumber/context/changes": [{"group": "3_git<PERSON>s", "order": 2}]}}, "gitlens.showLineHistoryView": {"label": "Show Line History View", "commandPalette": "gitlens:enabled && !gitlens:hasVirtualFolders", "menus": {"gitlens/views/grouped/fileHistory": [{"when": "gitlens:views:scm:grouped:view == fileHistory && !gitlens:hasVirtualFolders", "group": "8_gitlens_toggles", "order": 0}], "view/title": [{"when": "view == gitlens.views.fileHistory && !gitlens:hasVirtualFolders", "group": "8_gitlens_toggles", "order": 0}]}}, "gitlens.showPatchDetailsPage": {"label": "Show Patch Details", "commandPalette": "gitlens:enabled && gitlens:gk:organization:drafts:enabled && config.gitlens.cloudPatches.enabled"}, "gitlens.showQuickBranchHistory": {"label": "Show Branch History", "commandPalette": "gitlens:enabled"}, "gitlens.showQuickCommitDetails": {"label": "Quick Show Commit"}, "gitlens.showQuickCommitFileDetails": {"label": "Quick Show Line Commit", "commandPalette": "resource in gitlens:tabs:blameable", "menus": {"gitlens/editor/context/changes": [{"group": "3_git<PERSON>s", "order": 1}], "gitlens/editor/lineNumber/context/changes": [{"group": "3_git<PERSON>s", "order": 1}]}, "keybindings": [{"key": "alt+c", "when": "config.gitlens.keymap == alternate && editorTextFocus && !gitlens:disabled"}, {"key": "ctrl+shift+g c", "when": "config.gitlens.keymap == chorded && editorTextFocus && !gitlens:disabled", "mac": "cmd+alt+g c"}]}, "gitlens.showQuickFileHistory": {"label": "Show File History", "commandPalette": "resource in gitlens:tabs:tracked", "keybindings": [{"key": "alt+h", "when": "config.gitlens.keymap == alternate && !gitlens:disabled"}, {"key": "ctrl+shift+g h", "when": "config.gitlens.keymap == chorded && !gitlens:disabled", "mac": "cmd+alt+g h"}]}, "gitlens.showQuickRepoHistory": {"label": "Show Current Branch History", "commandPalette": "gitlens:enabled", "keybindings": [{"key": "ctrl+shift+g shift+h", "when": "config.gitlens.keymap == chorded && !gitlens:disabled", "mac": "cmd+alt+g shift+h"}, {"key": "shift+alt+h", "when": "config.gitlens.keymap == alternate && !gitlens:disabled"}]}, "gitlens.showQuickRepoStatus": {"label": "Show Repository Status", "commandPalette": "gitlens:enabled", "keybindings": [{"key": "alt+s", "when": "config.gitlens.keymap == alternate && !gitlens:disabled"}, {"key": "ctrl+shift+g s", "when": "config.gitlens.keymap == chorded && !gitlens:disabled", "mac": "cmd+alt+g s"}]}, "gitlens.showQuickRevisionDetails": {"label": "Show Revision Commit", "icon": "$(gitlens-commit-horizontal)", "enablement": "gitlens:enabled && resourceScheme =~ /^(gitlens|pr)$/", "commandPalette": "gitlens:enabled && resourceScheme =~ /^(gitlens|git|pr)$/", "menus": {"editor/title": [{"when": "resource in gitlens:tabs:tracked && config.gitlens.menus.editorGroup.compare", "group": "navigation", "order": 98}], "gitlens/editor/context/changes": [{"when": "resourceScheme =~ /^(gitlens|git|pr)$/ && gitlens:enabled", "group": "3_gitlens_1", "order": 1}]}}, "gitlens.showQuickStashList": {"label": "Show Stashes", "commandPalette": "gitlens:enabled"}, "gitlens.showRemotesView": {"label": "Show Remotes View", "commandPalette": "gitlens:enabled"}, "gitlens.showRepositoriesView": {"label": "Show Repositories View", "commandPalette": "gitlens:enabled && !gitlens:hasVirtualFolders", "menus": {"view/title": [{"when": "view == gitlens.views.commits && !gitlens:hasVirtualFolders", "group": "8_gitlens_toggles", "order": 1}]}}, "gitlens.showSearchAndCompareView": {"label": "Show Search & Compare View", "commandPalette": "gitlens:enabled"}, "gitlens.showSettingsPage": {"label": "Open Settings", "icon": "$(gear)", "commandPalette": true, "menus": {"extension/context": [{"when": "extension =~ /^eamodio.gitlens?$/ && extensionStatus == installed", "group": "9_git<PERSON>s", "order": 3}]}}, "gitlens.showSettingsPage!autolinks": {"label": "Configure Autolinks", "icon": "$(gear)", "commandPalette": true, "menus": {"view/item/context": [{"when": "viewItem =~ /gitlens:autolinked:items\\b/", "group": "inline", "order": 99}, {"when": "viewItem =~ /gitlens:autolinked:items\\b/ && !listMultiSelection", "group": "8_gitlens_actions", "order": 99}]}}, "gitlens.showSettingsPage!branches-view": {"label": "Open View Settings", "icon": "$(gear)", "menus": {"gitlens/views/grouped/more": [{"when": "gitlens:views:scm:grouped:view == branches && view == gitlens.views.scm.grouped", "group": "9_git<PERSON>s", "order": 1}], "view/title": [{"when": "view == gitlens.views.branches", "group": "9_git<PERSON>s", "order": 1}, {"when": "view == gitlens.views.scm.grouped && gitlens:views:scm:grouped:view == branches", "group": "9_git<PERSON>s", "order": 1}]}}, "gitlens.showSettingsPage!commit-graph": {"label": "Open Commit Graph Settings", "icon": "$(gear)", "menus": {"gitlens/graph/configuration": [{"group": "9_git<PERSON>s", "order": 1}]}}, "gitlens.showSettingsPage!commits-view": {"label": "Open View Settings", "icon": "$(gear)", "menus": {"gitlens/views/grouped/more": [{"when": "gitlens:views:scm:grouped:view == commits && view == gitlens.views.scm.grouped", "group": "9_git<PERSON>s", "order": 1}], "view/title": [{"when": "view == gitlens.views.commits", "group": "9_git<PERSON>s", "order": 1}, {"when": "view == gitlens.views.scm.grouped && gitlens:views:scm:grouped:view == commits", "group": "9_git<PERSON>s", "order": 1}]}}, "gitlens.showSettingsPage!contributors-view": {"label": "Open View Settings", "icon": "$(gear)", "menus": {"gitlens/views/grouped/more": [{"when": "gitlens:views:scm:grouped:view == contributors && view == gitlens.views.scm.grouped", "group": "9_git<PERSON>s", "order": 1}], "view/title": [{"when": "view == gitlens.views.contributors", "group": "9_git<PERSON>s", "order": 1}, {"when": "view == gitlens.views.scm.grouped && gitlens:views:scm:grouped:view == contributors", "group": "9_git<PERSON>s", "order": 1}]}}, "gitlens.showSettingsPage!file-annotations": {"label": "Open File Annotation Settings", "icon": "$(gear)", "menus": {"gitlens/editor/annotations": [{"when": "resource in gitlens:tabs:blameable", "group": "8_gitlens", "order": 1}]}}, "gitlens.showSettingsPage!file-history-view": {"label": "Open View Settings", "icon": "$(gear)", "menus": {"gitlens/views/grouped/more": [{"when": "gitlens:views:scm:grouped:view == fileHistory && view == gitlens.views.scm.grouped", "group": "9_git<PERSON>s", "order": 1}], "view/title": [{"when": "view == gitlens.views.fileHistory", "group": "9_git<PERSON>s", "order": 1}, {"when": "view == gitlens.views.scm.grouped && gitlens:views:scm:grouped:view == fileHistory", "group": "9_git<PERSON>s", "order": 1}]}}, "gitlens.showSettingsPage!line-history-view": {"label": "Open View Settings", "icon": "$(gear)", "menus": {"view/title": [{"when": "view == gitlens.views.lineHistory", "group": "9_git<PERSON>s", "order": 1}]}}, "gitlens.showSettingsPage!remotes-view": {"label": "Open View Settings", "icon": "$(gear)", "menus": {"gitlens/views/grouped/more": [{"when": "gitlens:views:scm:grouped:view == remotes && view == gitlens.views.scm.grouped", "group": "9_git<PERSON>s", "order": 1}], "view/title": [{"when": "view == gitlens.views.remotes", "group": "9_git<PERSON>s", "order": 1}, {"when": "view == gitlens.views.scm.grouped && gitlens:views:scm:grouped:view == remotes", "group": "9_git<PERSON>s", "order": 1}]}}, "gitlens.showSettingsPage!repositories-view": {"label": "Open View Settings", "icon": "$(gear)", "menus": {"gitlens/views/grouped/more": [{"when": "gitlens:views:scm:grouped:view == repositories && view == gitlens.views.scm.grouped", "group": "9_git<PERSON>s", "order": 1}], "view/title": [{"when": "view == gitlens.views.repositories", "group": "9_git<PERSON>s", "order": 1}, {"when": "view == gitlens.views.scm.grouped && gitlens:views:scm:grouped:view == repositories", "group": "9_git<PERSON>s", "order": 1}]}}, "gitlens.showSettingsPage!search-compare-view": {"label": "Open View Settings", "icon": "$(gear)", "menus": {"gitlens/views/grouped/more": [{"when": "gitlens:views:scm:grouped:view == searchAndCompare && view == gitlens.views.scm.grouped", "group": "9_git<PERSON>s", "order": 1}], "view/title": [{"when": "view == gitlens.views.scm.grouped && gitlens:views:scm:grouped:view == searchAndCompare", "group": "9_git<PERSON>s", "order": 1}, {"when": "view == gitlens.views.searchAndCompare", "group": "9_git<PERSON>s", "order": 1}]}}, "gitlens.showSettingsPage!stashes-view": {"label": "Open View Settings", "icon": "$(gear)", "menus": {"gitlens/views/grouped/more": [{"when": "gitlens:views:scm:grouped:view == stashes && view == gitlens.views.scm.grouped", "group": "9_git<PERSON>s", "order": 1}], "view/title": [{"when": "view == gitlens.views.scm.grouped && gitlens:views:scm:grouped:view == stashes", "group": "9_git<PERSON>s", "order": 1}, {"when": "view == gitlens.views.stashes", "group": "9_git<PERSON>s", "order": 1}]}}, "gitlens.showSettingsPage!tags-view": {"label": "Open View Settings", "icon": "$(gear)", "menus": {"gitlens/views/grouped/more": [{"when": "gitlens:views:scm:grouped:view == tags && view == gitlens.views.scm.grouped", "group": "9_git<PERSON>s", "order": 1}], "view/title": [{"when": "view == gitlens.views.scm.grouped && gitlens:views:scm:grouped:view == tags", "group": "9_git<PERSON>s", "order": 1}, {"when": "view == gitlens.views.tags", "group": "9_git<PERSON>s", "order": 1}]}}, "gitlens.showSettingsPage!views": {"label": "Open Settings", "icon": "$(gear)"}, "gitlens.showSettingsPage!worktrees-view": {"label": "Open View Settings", "icon": "$(gear)", "menus": {"gitlens/views/grouped/more": [{"when": "gitlens:views:scm:grouped:view == worktrees && view == gitlens.views.scm.grouped", "group": "9_git<PERSON>s", "order": 1}], "view/title": [{"when": "view == gitlens.views.scm.grouped && gitlens:views:scm:grouped:view == worktrees", "group": "9_git<PERSON>s", "order": 1}, {"when": "view == gitlens.views.worktrees", "group": "9_git<PERSON>s", "order": 1}]}}, "gitlens.showStashesView": {"label": "Show Stashes View", "commandPalette": "gitlens:enabled && !gitlens:hasVirtualFolders"}, "gitlens.showTagsView": {"label": "Show Tags View", "commandPalette": "gitlens:enabled"}, "gitlens.showTimelinePage": {"label": "Show Visual History", "icon": "$(graph-scatter)", "commandPalette": "gitlens:enabled"}, "gitlens.showTimelineView": {"label": "Show Visual File History View", "commandPalette": "gitlens:enabled"}, "gitlens.showWorkspacesView": {"label": "Show Cloud Workspaces View", "commandPalette": "gitlens:enabled && !gitlens:hasVirtualFolders"}, "gitlens.showWorktreesView": {"label": "Show Worktrees View", "commandPalette": "gitlens:enabled && !gitlens:hasVirtualFolders"}, "gitlens.startWork": {"label": "Start Work", "icon": "$(rocket)", "commandPalette": "gitlens:enabled"}, "gitlens.stashApply": {"label": "Apply a Stash...", "icon": "$(gitlens-stash-pop)", "enablement": "!operationInProgress", "commandPalette": "gitlens:enabled && !gitlens:readonly && !gitlens:untrusted && !gitlens:hasVirtualFolders", "menus": {"gitlens/views/grouped/stashes": [{"when": "gitlens:views:scm:grouped:view == stashes && !gitlens:readonly && !gitlens:untrusted && !gitlens:hasVirtualFolders", "group": "2_gitlens_actions", "order": 2}], "view/item/context": [{"when": "viewItem =~ /gitlens:repo-folder\\b/ && !listMultiSelection && !gitlens:readonly && !gitlens:untrusted && !gitlens:hasVirtualFolders", "group": "1_gitlens_actions_1", "order": 2}, {"when": "viewItem =~ /gitlens:repository\\b(?!.*?\\b\\+workspace\\b)/ && !listMultiSelection && !gitlens:readonly && !gitlens:untrusted && !gitlens:hasVirtualFolders", "group": "1_gitlens_actions_1", "order": 2}, {"when": "viewItem == gitlens:stashes && !gitlens:readonly && !gitlens:untrusted && !gitlens:hasVirtualFolders", "group": "inline", "order": 99}, {"when": "viewItem == gitlens:stashes && !listMultiSelection && !gitlens:readonly && !gitlens:untrusted && !gitlens:hasVirtualFolders", "group": "1_gitlens_actions", "order": 2}], "view/title": [{"when": "view == gitlens.views.stashes && !gitlens:readonly && !gitlens:untrusted && !gitlens:hasVirtualFolders", "group": "navigation", "order": 11}]}}, "gitlens.stashSave": {"label": "Stash All Changes...", "icon": "$(gitlens-stash-save)", "enablement": "!operationInProgress", "commandPalette": "gitlens:enabled && !gitlens:readonly && !gitlens:untrusted && !gitlens:hasVirtualFolders", "menus": {"gitlens/views/grouped/stashes": [{"when": "gitlens:views:scm:grouped:view == stashes && !gitlens:readonly && !gitlens:untrusted && !gitlens:hasVirtualFolders", "group": "2_gitlens_actions", "order": 1}], "scm/resourceGroup/context": [{"when": "scmResourceGroup =~ /^(workingTree|index)$/ && scmProvider == git && gitlens:enabled && !gitlens:readonly && !gitlens:untrusted && !gitlens:hasVirtualFolders && config.gitlens.menus.scmGroupInline.stash", "group": "inline", "order": -1}, {"when": "scmResourceGroup =~ /^(workingTree|index)$/ && scmProvider == git && gitlens:enabled && !gitlens:readonly && !gitlens:untrusted && !gitlens:hasVirtualFolders && config.gitlens.menus.scmGroup.stash", "group": "1_modification", "order": 100}], "scm/title": [{"when": "scmProvider == git && gitlens:enabled && !gitlens:readonly && !gitlens:untrusted && !gitlens:hasVirtualFolders && config.gitlens.menus.scmRepositoryInline.stash", "group": "navigation", "order": -2}], "view/item/context": [{"when": "viewItem =~ /gitlens:repo-folder\\b/ && !listMultiSelection && !gitlens:readonly && !gitlens:untrusted && !gitlens:hasVirtualFolders", "group": "1_gitlens_actions_1", "order": 1}, {"when": "viewItem =~ /gitlens:repository\\b(?!.*?\\b\\+workspace\\b)/ && !listMultiSelection && !gitlens:readonly && !gitlens:untrusted && !gitlens:hasVirtualFolders", "group": "1_gitlens_actions_1", "order": 1}, {"when": "viewItem =~ /^gitlens:(stashes|status:files)$/ && !gitlens:readonly && !gitlens:untrusted && !gitlens:hasVirtualFolders", "group": "inline", "order": 98}, {"when": "viewItem =~ /^gitlens:(stashes|status:files)$/ && !listMultiSelection && !gitlens:readonly && !gitlens:untrusted && !gitlens:hasVirtualFolders", "group": "1_gitlens_actions", "order": 1}], "view/title": [{"when": "view == gitlens.views.stashes && !gitlens:readonly && !gitlens:untrusted && !gitlens:hasVirtualFolders", "group": "navigation", "order": 10}]}}, "gitlens.stashSaveFiles": {"label": "Stash Changes...", "icon": "$(gitlens-stash-save)", "enablement": "!operationInProgress", "menus": {"scm/resourceState/context": [{"when": "scmResourceGroup =~ /^(workingTree|index)$/ && scmProvider == git && gitlens:enabled && !gitlens:readonly && !gitlens:untrusted && !gitlens:hasVirtualFolders && config.gitlens.menus.scmItemInline.stash", "group": "inline", "order": 1}, {"when": "scmResourceGroup =~ /^(workingTree|index)$/ && scmProvider == git && gitlens:enabled && !gitlens:readonly && !gitlens:untrusted && !gitlens:hasVirtualFolders && config.gitlens.menus.scmItem.stash", "group": "1_modification", "order": 2}], "view/item/context": [{"when": "viewItem =~ /gitlens:file\\b(?=.*?\\b\\+(un)?staged\\b)/ && !listMultiSelection && !gitlens:readonly && !gitlens:untrusted && !gitlens:hasVirtualFolders", "group": "1_gitlens_actions", "order": 2}]}}, "gitlens.switchMode": {"label": "Switch Mode", "commandPalette": "gitlens:enabled"}, "gitlens.timeline.refresh": {"label": "Refresh", "icon": "$(refresh)", "menus": {"editor/title": [{"when": "activeWebviewPanelId === gitlens.timeline", "group": "navigation", "order": -99}]}}, "gitlens.timeline.split": {"label": "Split Visual History", "icon": "$(split-horizontal)", "commandPalette": "gitlens:enabled && config.gitlens.visualHistory.allowMultiple", "menus": {"editor/title": [{"when": "activeWebviewPanelId == gitlens.timeline && resourceScheme == webview-panel && config.gitlens.visualHistory.allowMultiple", "group": "navigation", "order": -97}], "editor/title/context": [{"when": "activeWebviewPanelId == gitlens.timeline && resourceScheme == webview-panel && config.gitlens.visualHistory.allowMultiple", "group": "6_split_in_group_gitlens", "order": 2}]}}, "gitlens.toggleCodeLens": {"label": "Toggle Git CodeLens", "commandPalette": "!gitlens:disabled && !gitlens:disabledToggleCodeLens", "keybindings": [{"key": "ctrl+shift+g shift+b", "when": "config.gitlens.keymap == chorded && editorTextFocus && !gitlens:disabled && !gitlens:disabledToggleCodeLens", "mac": "cmd+alt+g shift+b"}, {"key": "shift+alt+b", "when": "config.gitlens.keymap == alternate && editorTextFocus && !gitlens:disabled && !gitlens:disabledToggleCodeLens"}]}, "gitlens.toggleFileBlame": {"label": "Toggle File Blame", "icon": "$(gitlens-gitlens)", "commandPalette": "resource in gitlens:tabs:blameable || config.gitlens.blame.toggleMode == window", "menus": {"editor/title": [{"when": "resource in gitlens:tabs:blameable && resource not in gitlens:tabs:annotated && config.gitlens.menus.editorGroup.blame && config.gitlens.fileAnnotations.command == blame", "group": "navigation", "order": 100, "alt": "gitlens.toggleFileHeatmap"}], "gitlens/editor/annotations": [{"when": "resource in gitlens:tabs:blameable", "group": "2_git<PERSON>s", "order": 1}]}, "keybindings": [{"key": "alt+b", "when": "config.gitlens.keymap == alternate && editorTextFocus && resource in gitlens:tabs:blameable"}, {"key": "ctrl+shift+g b", "when": "config.gitlens.keymap == chorded && editorTextFocus && resource in gitlens:tabs:blameable", "mac": "cmd+alt+g b"}]}, "gitlens.toggleFileChanges": {"label": "Toggle File Changes", "icon": "$(gitlens-gitlens)", "commandPalette": "(resource in gitlens:tabs:blameable  || config.gitlens.changes.toggleMode == window) && !gitlens:hasVirtualFolders", "menus": {"editor/title": [{"when": "resource in gitlens:tabs:blameable && resource not in gitlens:tabs:annotated && !gitlens:hasVirtualFolders && config.gitlens.menus.editorGroup.blame && config.gitlens.fileAnnotations.command == changes", "group": "navigation", "order": 100, "alt": "gitlens.toggleFileBlame"}], "gitlens/editor/annotations": [{"when": "resource in gitlens:tabs:blameable && !gitlens:hasVirtualFolders", "group": "2_git<PERSON>s", "order": 3}]}}, "gitlens.toggleFileChangesOnly": {"label": "Toggle File Changes", "icon": "$(gitlens-gitlens)"}, "gitlens.toggleFileHeatmap": {"label": "Toggle File Heatmap", "icon": "$(gitlens-gitlens)", "commandPalette": "resource in gitlens:tabs:blameable || config.gitlens.heatmap.toggleMode == window", "menus": {"editor/title": [{"when": "resource in gitlens:tabs:blameable && resource not in gitlens:tabs:annotated && config.gitlens.menus.editorGroup.blame && config.gitlens.fileAnnotations.command == heatmap", "group": "navigation", "order": 100, "alt": "gitlens.toggleFileBlame"}], "gitlens/editor/annotations": [{"when": "resource in gitlens:tabs:blameable && !isInDiffEditor", "group": "2_git<PERSON>s", "order": 2}]}}, "gitlens.toggleFileHeatmapInDiffLeft": {"label": "Toggle File Heatmap", "icon": "$(gitlens-gitlens)", "menus": {"gitlens/editor/annotations": [{"when": "resource in gitlens:tabs:blameable && isInDiffEditor && !isInDiffRightEditor", "group": "2_git<PERSON>s", "order": 2}]}}, "gitlens.toggleFileHeatmapInDiffRight": {"label": "Toggle File Heatmap", "icon": "$(gitlens-gitlens)", "menus": {"gitlens/editor/annotations": [{"when": "resource in gitlens:tabs:blameable && isInDiffRightEditor", "group": "2_git<PERSON>s", "order": 2}]}}, "gitlens.toggleGraph": {"label": "Toggle Commit Graph", "icon": "$(gitlens-graph)", "commandPalette": "gitlens:enabled"}, "gitlens.toggleLineBlame": {"label": "Toggle Line Blame", "commandPalette": "!gitlens:disabled"}, "gitlens.toggleMaximizedGraph": {"label": "Toggle Maximized Commit Graph", "icon": "$(gitlens-graph)", "commandPalette": "gitlens:enabled"}, "gitlens.toggleReviewMode": {"label": "Toggle Review Mode", "commandPalette": "gitlens:enabled"}, "gitlens.toggleZenMode": {"label": "Toggle Zen Mode", "commandPalette": "gitlens:enabled"}, "gitlens.views.abortPausedOperation": {"label": "Abort", "icon": "$(circle-slash)", "menus": {"view/item/context": [{"when": "viewItem =~ /gitlens:paused-operation\\b/ && !listMultiSelection", "group": "inline", "order": 3}, {"when": "viewItem =~ /gitlens:paused-operation\\b/ && !listMultiSelection", "group": "1_gitlens_actions", "order": 3}]}}, "gitlens.views.addAuthor": {"label": "Add as Co-author", "icon": "$(person-add)", "menus": {"view/item/context": [{"when": "viewItem =~ /gitlens:contributor\\b(?!.*?\\b\\+current\\b)/ && !gitlens:readonly && !gitlens:untrusted && !gitlens:hasVirtualFolders", "group": "inline", "order": 2}, {"when": "viewItem =~ /gitlens:contributor\\b(?!.*?\\b\\+current\\b)/ && !listMultiSelection && !gitlens:readonly && !gitlens:untrusted && !gitlens:hasVirtualFolders", "group": "1_gitlens_actions", "order": 2}]}}, "gitlens.views.addAuthor.multi": {"label": "Add as Co-authors", "icon": "$(person-add)", "menus": {"view/item/context": [{"when": "viewItem =~ /gitlens:contributor\\b(?!.*?\\b\\+current\\b)/ && listMultiSelection && !gitlens:readonly && !gitlens:untrusted && !gitlens:hasVirtualFolders", "group": "1_gitlens_actions", "order": 2}]}}, "gitlens.views.addAuthors": {"label": "Add Co-authors...", "icon": "$(person-add)", "menus": {"gitlens/views/grouped/contributors": [{"when": "gitlens:views:scm:grouped:view == contributors && !gitlens:readonly && !gitlens:untrusted && !gitlens:hasVirtualFolders", "group": "2_gitlens_actions", "order": 1}], "view/item/context": [{"when": "viewItem =~ /gitlens:contributors\\b/ && !gitlens:readonly && !gitlens:untrusted && !gitlens:hasVirtualFolders", "group": "inline", "order": 1}, {"when": "viewItem =~ /gitlens:contributors\\b/ && !listMultiSelection && !gitlens:readonly && !gitlens:untrusted && !gitlens:hasVirtualFolders", "group": "1_gitlens_actions", "order": 1}], "view/title": [{"when": "view == gitlens.views.contributors && !gitlens:readonly && !gitlens:untrusted && !gitlens:hasVirtualFolders", "group": "navigation", "order": 10}]}}, "gitlens.views.addPullRequestRemote": {"label": "Add Pull Request Remote", "icon": "$(add)", "enablement": "!operationInProgress"}, "gitlens.views.addRemote": {"label": "Add Remote...", "icon": "$(add)", "enablement": "!operationInProgress", "menus": {"gitlens/views/grouped/remotes": [{"when": "gitlens:views:scm:grouped:view == remotes && !gitlens:readonly && !gitlens:untrusted && !gitlens:hasVirtualFolders", "group": "2_gitlens_actions", "order": 1}], "view/item/context": [{"when": "viewItem =~ /gitlens:remotes\\b/ && !gitlens:readonly && !gitlens:untrusted && !gitlens:hasVirtualFolders", "group": "inline", "order": 1}, {"when": "viewItem =~ /gitlens:remotes\\b/ && !listMultiSelection && !gitlens:readonly && !gitlens:untrusted && !gitlens:hasVirtualFolders", "group": "1_gitlens_actions", "order": 1}, {"when": "viewItem =~ /gitlens:status(\\-branch)?:upstream:none/ && !gitlens:readonly && !gitlens:untrusted && !gitlens:hasVirtualFolders && !gitlens:repos:withRemotes", "group": "inline", "order": 2}], "view/title": [{"when": "view == gitlens.views.remotes && !gitlens:readonly && !gitlens:untrusted && !gitlens:hasVirtualFolders", "group": "navigation", "order": 1}]}}, "gitlens.views.applyChanges": {"label": "Apply Changes", "enablement": "!operationInProgress", "menus": {"view/item/context": [{"when": "viewItem =~ /gitlens:file\\b(?=.*?\\b\\+stashed\\b)/ && !listMultiSelection && !gitlens:readonly && !gitlens:untrusted && !gitlens:hasVirtualFolders", "group": "1_gitlens_actions", "order": 1}, {"when": "viewItem =~ /gitlens:file\\b((?=.*?\\b\\+committed\\b)|:results\\b)/ && !listMultiSelection && !gitlens:readonly && !gitlens:untrusted && !gitlens:hasVirtualFolders", "group": "8_gitlens_actions", "order": 1}]}}, "gitlens.views.associateIssueWithBranch": {"label": "Associate Issue with Branch...", "enablement": "!operationInProgress", "menus": {"view/item/context": [{"when": "viewItem =~ /gitlens:branch\\b(?!.*?\\b\\+remote\\b)/ && !listMultiSelection && !gitlens:readonly && !gitlens:untrusted && !gitlens:hasVirtualFolders", "group": "1_gitlens_actions_2", "order": 1}]}}, "gitlens.views.branches.attach": {"label": "Group into GitLens View", "icon": "$(close)", "menus": {"view/title": [{"when": "view == gitlens.views.branches", "group": "navigation", "order": 99}, {"when": "view == gitlens.views.branches", "group": "9_git<PERSON>s", "order": 0}]}}, "gitlens.views.branches.copy": {"label": "Copy", "keybindings": [{"key": "ctrl+c", "when": "focusedView == gitlens.views.branches", "mac": "cmd+c"}]}, "gitlens.views.branches.refresh": {"label": "Refresh", "icon": "$(refresh)", "menus": {"view/title": [{"when": "view == gitlens.views.branches", "group": "navigation", "order": 98}]}}, "gitlens.views.branches.setFilesLayoutToAuto": {"label": "View Files as Auto", "icon": "$(list-tree)", "enablement": "config.gitlens.views.branches.files.layout != auto", "menus": {"gitlens/views/grouped/more": [{"when": "gitlens:views:scm:grouped:view == branches && view == gitlens.views.scm.grouped", "group": "3_git<PERSON><PERSON>_", "order": 1}], "view/title": [{"when": "view == gitlens.views.branches", "group": "3_git<PERSON><PERSON>_", "order": 0}, {"when": "view == gitlens.views.scm.grouped && gitlens:views:scm:grouped:view == branches", "group": "3_git<PERSON><PERSON>_", "order": 1}]}}, "gitlens.views.branches.setFilesLayoutToList": {"label": "View Files as List", "icon": "$(gitlens-list-auto)", "enablement": "config.gitlens.views.branches.files.layout != list", "menus": {"gitlens/views/grouped/more": [{"when": "gitlens:views:scm:grouped:view == branches && view == gitlens.views.scm.grouped", "group": "3_git<PERSON><PERSON>_", "order": 1}], "view/title": [{"when": "view == gitlens.views.branches", "group": "3_git<PERSON><PERSON>_", "order": 0}, {"when": "view == gitlens.views.scm.grouped && gitlens:views:scm:grouped:view == branches", "group": "3_git<PERSON><PERSON>_", "order": 1}]}}, "gitlens.views.branches.setFilesLayoutToTree": {"label": "View Files as Tree", "icon": "$(list-flat)", "enablement": "config.gitlens.views.branches.files.layout != tree", "menus": {"gitlens/views/grouped/more": [{"when": "gitlens:views:scm:grouped:view == branches && view == gitlens.views.scm.grouped", "group": "3_git<PERSON><PERSON>_", "order": 1}], "view/title": [{"when": "view == gitlens.views.branches", "group": "3_git<PERSON><PERSON>_", "order": 0}, {"when": "view == gitlens.views.scm.grouped && gitlens:views:scm:grouped:view == branches", "group": "3_git<PERSON><PERSON>_", "order": 1}]}}, "gitlens.views.branches.setLayoutToList": {"label": "View as List", "icon": "$(list-tree)", "enablement": "config.gitlens.views.branches.branches.layout != list", "menus": {"gitlens/views/grouped/more": [{"when": "gitlens:views:scm:grouped:view == branches && view == gitlens.views.scm.grouped", "group": "3_git<PERSON>s", "order": 0}], "view/title": [{"when": "view == gitlens.views.branches && config.gitlens.views.branches.branches.layout == tree", "group": "navigation", "order": 50}, {"when": "view == gitlens.views.branches", "group": "3_git<PERSON>s", "order": 0}, {"when": "view == gitlens.views.scm.grouped && gitlens:views:scm:grouped:view == branches", "group": "3_git<PERSON>s", "order": 0}]}}, "gitlens.views.branches.setLayoutToTree": {"label": "View as Tree", "icon": "$(list-flat)", "enablement": "config.gitlens.views.branches.branches.layout != tree", "menus": {"gitlens/views/grouped/more": [{"when": "gitlens:views:scm:grouped:view == branches && view == gitlens.views.scm.grouped", "group": "3_git<PERSON>s", "order": 0}], "view/title": [{"when": "view == gitlens.views.branches && config.gitlens.views.branches.branches.layout == list", "group": "navigation", "order": 50}, {"when": "view == gitlens.views.branches", "group": "3_git<PERSON>s", "order": 0}, {"when": "view == gitlens.views.scm.grouped && gitlens:views:scm:grouped:view == branches", "group": "3_git<PERSON>s", "order": 0}]}}, "gitlens.views.branches.setShowAvatarsOff": {"label": "Hide Avatars", "menus": {"gitlens/views/grouped/more": [{"when": "gitlens:views:scm:grouped:view == branches && view == gitlens.views.scm.grouped && config.gitlens.views.branches.avatars", "group": "5_gitlens", "order": 1}], "view/title": [{"when": "view == gitlens.views.branches && config.gitlens.views.branches.avatars", "group": "5_gitlens", "order": 1}, {"when": "view == gitlens.views.scm.grouped && gitlens:views:scm:grouped:view == branches && config.gitlens.views.branches.avatars", "group": "5_gitlens", "order": 1}]}}, "gitlens.views.branches.setShowAvatarsOn": {"label": "Show Avatars", "menus": {"gitlens/views/grouped/more": [{"when": "gitlens:views:scm:grouped:view == branches && view == gitlens.views.scm.grouped && !config.gitlens.views.branches.avatars", "group": "5_gitlens", "order": 1}], "view/title": [{"when": "view == gitlens.views.branches && !config.gitlens.views.branches.avatars", "group": "5_gitlens", "order": 1}, {"when": "view == gitlens.views.scm.grouped && gitlens:views:scm:grouped:view == branches && !config.gitlens.views.branches.avatars", "group": "5_gitlens", "order": 1}]}}, "gitlens.views.branches.setShowBranchComparisonOff": {"label": "Hide Branch Comparisons", "menus": {"gitlens/views/grouped/more": [{"when": "gitlens:views:scm:grouped:view == branches && view == gitlens.views.scm.grouped && config.gitlens.views.branches.showBranchComparison", "group": "5_gitlens", "order": 2}], "view/item/context": [{"when": "viewItem =~ /gitlens:compare:branch\\b/ && view == gitlens.views.branches && !listMultiSelection", "group": "8_gitlens_toggles", "order": 1}], "view/title": [{"when": "view == gitlens.views.branches && config.gitlens.views.branches.showBranchComparison", "group": "5_gitlens", "order": 2}, {"when": "view == gitlens.views.scm.grouped && gitlens:views:scm:grouped:view == branches && config.gitlens.views.branches.showBranchComparison", "group": "5_gitlens", "order": 2}]}}, "gitlens.views.branches.setShowBranchComparisonOn": {"label": "Show Branch Comparisons", "menus": {"gitlens/views/grouped/more": [{"when": "gitlens:views:scm:grouped:view == branches && view == gitlens.views.scm.grouped && !config.gitlens.views.branches.showBranchComparison", "group": "5_gitlens", "order": 2}], "view/title": [{"when": "view == gitlens.views.branches && !config.gitlens.views.branches.showBranchComparison", "group": "5_gitlens", "order": 2}, {"when": "view == gitlens.views.scm.grouped && gitlens:views:scm:grouped:view == branches && !config.gitlens.views.branches.showBranchComparison", "group": "5_gitlens", "order": 2}]}}, "gitlens.views.branches.setShowBranchPullRequestOff": {"label": "Hide Branch Pull Requests", "menus": {"gitlens/views/grouped/more": [{"when": "gitlens:views:scm:grouped:view == branches && view == gitlens.views.scm.grouped && config.gitlens.views.branches.pullRequests.enabled && config.gitlens.views.branches.pullRequests.showForBranches", "group": "5_gitlens", "order": 3}], "view/title": [{"when": "view == gitlens.views.branches && config.gitlens.views.branches.pullRequests.enabled && config.gitlens.views.branches.pullRequests.showForBranches", "group": "5_gitlens", "order": 3}, {"when": "view == gitlens.views.scm.grouped && gitlens:views:scm:grouped:view == branches && config.gitlens.views.branches.pullRequests.enabled && config.gitlens.views.branches.pullRequests.showForBranches", "group": "5_gitlens", "order": 3}]}}, "gitlens.views.branches.setShowBranchPullRequestOn": {"label": "Show Branch Pull Requests", "menus": {"gitlens/views/grouped/more": [{"when": "gitlens:views:scm:grouped:view == branches && view == gitlens.views.scm.grouped && !config.gitlens.views.branches.pullRequests.enabled && !config.gitlens.views.branches.pullRequests.showForBranches", "group": "5_gitlens", "order": 3}], "view/title": [{"when": "view == gitlens.views.branches && !config.gitlens.views.branches.pullRequests.enabled && !config.gitlens.views.branches.pullRequests.showForBranches", "group": "5_gitlens", "order": 3}, {"when": "view == gitlens.views.scm.grouped && gitlens:views:scm:grouped:view == branches && !config.gitlens.views.branches.pullRequests.enabled && !config.gitlens.views.branches.pullRequests.showForBranches", "group": "5_gitlens", "order": 3}]}}, "gitlens.views.branches.setShowRemoteBranchesOff": {"label": "Hide Remote Branches", "menus": {"gitlens/views/grouped/more": [{"when": "gitlens:views:scm:grouped:view == branches && view == gitlens.views.scm.grouped && !gitlens:hasVirtualFolders && config.gitlens.views.branches.showRemoteBranches", "group": "5_gitlens", "order": 5}], "view/title": [{"when": "view == gitlens.views.branches && !gitlens:hasVirtualFolders && config.gitlens.views.branches.showRemoteBranches", "group": "5_gitlens", "order": 5}, {"when": "view == gitlens.views.scm.grouped && gitlens:views:scm:grouped:view == branches && !gitlens:hasVirtualFolders && config.gitlens.views.branches.showRemoteBranches", "group": "5_gitlens", "order": 5}]}}, "gitlens.views.branches.setShowRemoteBranchesOn": {"label": "Show Remote Branches", "menus": {"gitlens/views/grouped/more": [{"when": "gitlens:views:scm:grouped:view == branches && view == gitlens.views.scm.grouped && !gitlens:hasVirtualFolders && !config.gitlens.views.branches.showRemoteBranches", "group": "5_gitlens", "order": 5}], "view/title": [{"when": "view == gitlens.views.branches && !gitlens:hasVirtualFolders && !config.gitlens.views.branches.showRemoteBranches", "group": "5_gitlens", "order": 5}, {"when": "view == gitlens.views.scm.grouped && gitlens:views:scm:grouped:view == branches && !gitlens:hasVirtualFolders && !config.gitlens.views.branches.showRemoteBranches", "group": "5_gitlens", "order": 5}]}}, "gitlens.views.branches.setShowStashesOff": {"label": "<PERSON><PERSON> St<PERSON>", "menus": {"gitlens/views/grouped/more": [{"when": "gitlens:views:scm:grouped:view == branches && view == gitlens.views.scm.grouped && !gitlens:hasVirtualFolders && config.gitlens.views.branches.showStashes", "group": "5_gitlens", "order": 6}], "view/title": [{"when": "view == gitlens.views.branches && !gitlens:hasVirtualFolders && config.gitlens.views.branches.showStashes", "group": "5_gitlens", "order": 6}, {"when": "view == gitlens.views.scm.grouped && gitlens:views:scm:grouped:view == branches && !gitlens:hasVirtualFolders && config.gitlens.views.branches.showStashes", "group": "5_gitlens", "order": 6}]}}, "gitlens.views.branches.setShowStashesOn": {"label": "Show Stashes", "menus": {"gitlens/views/grouped/more": [{"when": "gitlens:views:scm:grouped:view == branches && view == gitlens.views.scm.grouped && !gitlens:hasVirtualFolders && !config.gitlens.views.branches.showStashes", "group": "5_gitlens", "order": 6}], "view/title": [{"when": "view == gitlens.views.branches && !gitlens:hasVirtualFolders && !config.gitlens.views.branches.showStashes", "group": "5_gitlens", "order": 6}, {"when": "view == gitlens.views.scm.grouped && gitlens:views:scm:grouped:view == branches && !gitlens:hasVirtualFolders && !config.gitlens.views.branches.showStashes", "group": "5_gitlens", "order": 6}]}}, "gitlens.views.branches.viewOptionsTitle": {"label": "Branches View Options", "enablement": "false", "menus": {"view/title": [{"when": "view == gitlens.views.scm.grouped && gitlens:views:scm:grouped:view == branches", "group": "1_gitlens_1", "order": 1}]}}, "gitlens.views.browseRepoAtRevision": {"label": "Repository from Here", "icon": "$(folder-opened)", "menus": {"gitlens/commit/browse": [{"group": "1_gitlens", "order": 1}]}}, "gitlens.views.browseRepoAtRevisionInNewWindow": {"label": "Repository from Here in New Window", "icon": "$(folder-opened)", "menus": {"gitlens/commit/browse": [{"group": "1_gitlens", "order": 3}]}}, "gitlens.views.browseRepoBeforeRevision": {"label": "Repository from Before Here", "icon": "$(folder-opened)", "menus": {"gitlens/commit/browse": [{"group": "1_gitlens", "order": 2}]}}, "gitlens.views.browseRepoBeforeRevisionInNewWindow": {"label": "Repository from Before Here in New Window", "icon": "$(folder-opened)", "menus": {"gitlens/commit/browse": [{"group": "1_gitlens", "order": 4}]}}, "gitlens.views.cherryPick": {"label": "Cherry Pick Commit...", "enablement": "!operationInProgress", "menus": {"view/item/context": [{"when": "viewItem =~ /gitlens:commit\\b(?!.*?\\b\\+(current|rebase)\\b)/ && !listMultiSelection && !gitlens:readonly && !gitlens:untrusted && !gitlens:hasVirtualFolders", "group": "1_gitlens_actions", "order": 1}]}}, "gitlens.views.cherryPick.multi": {"label": "Cherry Pick Commits...", "enablement": "!operationInProgress", "menus": {"view/item/context": [{"when": "viewItem =~ /gitlens:commit\\b(?!.*?\\b\\+(current|rebase)\\b)/ && listMultiSelection && !gitlens:readonly && !gitlens:untrusted && !gitlens:hasVirtualFolders", "group": "1_gitlens_actions", "order": 1}]}}, "gitlens.views.clearComparison": {"label": "Clear Comparison", "icon": "$(close)", "menus": {"view/item/context": [{"when": "viewItem =~ /gitlens:compare:branch\\b(?=.*?\\b\\+comparing\\b)/", "group": "inline", "order": 99}, {"when": "viewItem =~ /gitlens:compare:branch\\b(?=.*?\\b\\+comparing\\b)/ && !listMultiSelection", "group": "1_gitlens", "order": 4}]}}, "gitlens.views.clearReviewed": {"label": "Clear Reviewed Files", "menus": {"view/item/context": [{"when": "viewItem =~ /gitlens:compare:branch\\b(?=.*?\\b\\+comparing\\b)/ && !listMultiSelection", "group": "1_gitlens", "order": 3}, {"when": "viewItem =~ /gitlens:compare:results(?!:)/ && !listMultiSelection", "group": "1_gitlens_actions", "order": 3}, {"when": "viewItem =~ /gitlens:results:files\\b/ && !listMultiSelection", "group": "1_gitlens", "order": 1}]}}, "gitlens.views.closeRepository": {"label": "Close Repository", "menus": {"view/item/context": [{"when": "viewItem =~ /gitlens:repo-folder\\b/ && !listMultiSelection", "group": "8_gitlens_actions_", "order": 2}, {"when": "viewItem =~ /gitlens:repository\\b(?!.*?\\b\\+closed\\b)/ && !listMultiSelection", "group": "8_gitlens_actions_", "order": 2}]}}, "gitlens.views.collapseNode": {"label": "Collapse", "menus": {"view/item/context": [{"when": "viewItem =~ /gitlens:(branch|commit|compare|contributor|folder|grouping|launchpad:item|pseudo:folder|pullrequest|remote|results|search|stash|status|tag|worktree)\\b/ && !listMultiSelection", "group": "9_git<PERSON>s", "order": 1}]}}, "gitlens.views.commitDetails.refresh": {"label": "Refresh", "icon": "$(refresh)", "menus": {"view/title": [{"when": "view == gitlens.views.commitDetails", "group": "navigation", "order": 99}]}}, "gitlens.views.commits.attach": {"label": "Group into GitLens View", "icon": "$(close)", "menus": {"view/title": [{"when": "view == gitlens.views.commits", "group": "navigation", "order": 99}, {"when": "view == gitlens.views.commits", "group": "9_git<PERSON>s", "order": 0}]}}, "gitlens.views.commits.copy": {"label": "Copy", "keybindings": [{"key": "ctrl+c", "when": "focusedView == gitlens.views.commits", "mac": "cmd+c"}]}, "gitlens.views.commits.refresh": {"label": "Refresh", "icon": "$(refresh)", "menus": {"view/title": [{"when": "view == gitlens.views.commits", "group": "navigation", "order": 98}]}}, "gitlens.views.commits.setCommitsFilterAuthors": {"label": "Filter Commits by Author...", "icon": "$(filter)", "menus": {"gitlens/views/grouped/commits": [{"when": "gitlens:views:scm:grouped:view == commits", "group": "8_git<PERSON><PERSON>_", "order": 1}], "view/item/context": [{"when": "viewItem == gitlens:commits:current-branch && !gitlens:views:commits:filtered", "group": "inline", "order": 50}, {"when": "!listMultiSelection && (view == gitlens.views.commits || (view == gitlens.views.scm.grouped && gitlens:views:scm:grouped:view == commits)) && viewItem =~ /gitlens:repo-folder\\b/", "group": "8_gitlens_filter_", "order": 2}], "view/title": [{"when": "view == gitlens.views.commits", "group": "2_git<PERSON>s", "order": 1}]}}, "gitlens.views.commits.setCommitsFilterOff": {"label": "Clear Filter", "icon": "$(filter-filled)", "menus": {"gitlens/views/grouped/commits": [{"when": "gitlens:views:scm:grouped:view == commits && gitlens:views:commits:filtered", "group": "8_git<PERSON><PERSON>_", "order": 0}], "view/item/context": [{"when": "viewItem == gitlens:commits:current-branch && gitlens:views:commits:filtered", "group": "inline", "order": 50}, {"when": "(view == gitlens.views.commits || (view == gitlens.views.scm.grouped && gitlens:views:scm:grouped:view == commits)) && viewItem =~ /gitlens:repo-folder\\b(?=.*?\\b\\+filtered\\b)/ && gitlens:views:commits:filtered", "group": "inline", "order": 101}, {"when": "!listMultiSelection && (view == gitlens.views.commits || (view == gitlens.views.scm.grouped && gitlens:views:scm:grouped:view == commits)) && viewItem =~ /gitlens:repo-folder\\b(?=.*?\\b\\+filtered\\b)/ && gitlens:views:commits:filtered", "group": "8_gitlens_filter_", "order": 1}], "view/title": [{"when": "view == gitlens.views.commits && gitlens:views:commits:filtered", "group": "navigation", "order": 50}, {"when": "view == gitlens.views.commits && gitlens:views:commits:filtered", "group": "2_git<PERSON>s", "order": 0}]}}, "gitlens.views.commits.setFilesLayoutToAuto": {"label": "View Files as Auto", "icon": "$(list-tree)", "enablement": "config.gitlens.views.commits.files.layout != auto", "menus": {"gitlens/views/grouped/more": [{"when": "gitlens:views:scm:grouped:view == commits && view == gitlens.views.scm.grouped", "group": "3_git<PERSON><PERSON>_", "order": 2}], "view/title": [{"when": "view == gitlens.views.commits", "group": "3_git<PERSON><PERSON>_", "order": 2}, {"when": "view == gitlens.views.scm.grouped && gitlens:views:scm:grouped:view == commits", "group": "3_git<PERSON><PERSON>_", "order": 2}]}}, "gitlens.views.commits.setFilesLayoutToList": {"label": "View Files as List", "icon": "$(gitlens-list-auto)", "enablement": "config.gitlens.views.commits.files.layout != list", "menus": {"gitlens/views/grouped/more": [{"when": "gitlens:views:scm:grouped:view == commits && view == gitlens.views.scm.grouped", "group": "3_git<PERSON><PERSON>_", "order": 2}], "view/title": [{"when": "view == gitlens.views.commits", "group": "3_git<PERSON><PERSON>_", "order": 2}, {"when": "view == gitlens.views.scm.grouped && gitlens:views:scm:grouped:view == commits", "group": "3_git<PERSON><PERSON>_", "order": 2}]}}, "gitlens.views.commits.setFilesLayoutToTree": {"label": "View Files as Tree", "icon": "$(list-flat)", "enablement": "config.gitlens.views.commits.files.layout != tree", "menus": {"gitlens/views/grouped/more": [{"when": "gitlens:views:scm:grouped:view == commits && view == gitlens.views.scm.grouped", "group": "3_git<PERSON><PERSON>_", "order": 2}], "view/title": [{"when": "view == gitlens.views.commits", "group": "3_git<PERSON><PERSON>_", "order": 2}, {"when": "view == gitlens.views.scm.grouped && gitlens:views:scm:grouped:view == commits", "group": "3_git<PERSON><PERSON>_", "order": 2}]}}, "gitlens.views.commits.setShowAvatarsOff": {"label": "Hide Avatars", "menus": {"gitlens/views/grouped/more": [{"when": "gitlens:views:scm:grouped:view == commits && view == gitlens.views.scm.grouped && config.gitlens.views.commits.avatars", "group": "5_gitlens", "order": 1}], "view/title": [{"when": "view == gitlens.views.commits && config.gitlens.views.commits.avatars", "group": "5_gitlens", "order": 1}, {"when": "view == gitlens.views.scm.grouped && gitlens:views:scm:grouped:view == commits && config.gitlens.views.commits.avatars", "group": "5_gitlens", "order": 1}]}}, "gitlens.views.commits.setShowAvatarsOn": {"label": "Show Avatars", "menus": {"gitlens/views/grouped/more": [{"when": "gitlens:views:scm:grouped:view == commits && view == gitlens.views.scm.grouped && !config.gitlens.views.commits.avatars", "group": "5_gitlens", "order": 1}], "view/title": [{"when": "view == gitlens.views.commits && !config.gitlens.views.commits.avatars", "group": "5_gitlens", "order": 1}, {"when": "view == gitlens.views.scm.grouped && gitlens:views:scm:grouped:view == commits && !config.gitlens.views.commits.avatars", "group": "5_gitlens", "order": 1}]}}, "gitlens.views.commits.setShowBranchComparisonOff": {"label": "Hide Branch Comparison", "menus": {"gitlens/views/grouped/more": [{"when": "gitlens:views:scm:grouped:view == commits && view == gitlens.views.scm.grouped && config.gitlens.views.commits.showBranchComparison", "group": "5_gitlens", "order": 2}], "view/item/context": [{"when": "viewItem =~ /gitlens:compare:branch\\b/ && view == gitlens.views.commits && !listMultiSelection", "group": "8_gitlens_toggles", "order": 1}], "view/title": [{"when": "view == gitlens.views.commits && config.gitlens.views.commits.showBranchComparison", "group": "5_gitlens", "order": 2}, {"when": "view == gitlens.views.scm.grouped && gitlens:views:scm:grouped:view == commits && config.gitlens.views.commits.showBranchComparison", "group": "5_gitlens", "order": 2}]}}, "gitlens.views.commits.setShowBranchComparisonOn": {"label": "Show Branch Comparison", "menus": {"gitlens/views/grouped/more": [{"when": "gitlens:views:scm:grouped:view == commits && view == gitlens.views.scm.grouped && !config.gitlens.views.commits.showBranchComparison", "group": "5_gitlens", "order": 2}], "view/title": [{"when": "view == gitlens.views.commits && !config.gitlens.views.commits.showBranchComparison", "group": "5_gitlens", "order": 2}, {"when": "view == gitlens.views.scm.grouped && gitlens:views:scm:grouped:view == commits && !config.gitlens.views.commits.showBranchComparison", "group": "5_gitlens", "order": 2}]}}, "gitlens.views.commits.setShowBranchPullRequestOff": {"label": "Hide Current Branch Pull Request", "menus": {"gitlens/views/grouped/more": [{"when": "gitlens:views:scm:grouped:view == commits && view == gitlens.views.scm.grouped && config.gitlens.views.commits.pullRequests.enabled && config.gitlens.views.commits.pullRequests.showForBranches", "group": "5_gitlens", "order": 3}], "view/title": [{"when": "view == gitlens.views.commits && config.gitlens.views.commits.pullRequests.enabled && config.gitlens.views.commits.pullRequests.showForBranches", "group": "5_gitlens", "order": 3}, {"when": "view == gitlens.views.scm.grouped && gitlens:views:scm:grouped:view == commits && config.gitlens.views.commits.pullRequests.enabled && config.gitlens.views.commits.pullRequests.showForBranches", "group": "5_gitlens", "order": 3}]}}, "gitlens.views.commits.setShowBranchPullRequestOn": {"label": "Show Current Branch Pull Request", "menus": {"gitlens/views/grouped/more": [{"when": "gitlens:views:scm:grouped:view == commits && view == gitlens.views.scm.grouped && !config.gitlens.views.commits.pullRequests.enabled && !config.gitlens.views.commits.pullRequests.showForBranches", "group": "5_gitlens", "order": 3}], "view/title": [{"when": "view == gitlens.views.commits && !config.gitlens.views.commits.pullRequests.enabled && !config.gitlens.views.commits.pullRequests.showForBranches", "group": "5_gitlens", "order": 3}, {"when": "view == gitlens.views.scm.grouped && gitlens:views:scm:grouped:view == commits && !config.gitlens.views.commits.pullRequests.enabled && !config.gitlens.views.commits.pullRequests.showForBranches", "group": "5_gitlens", "order": 3}]}}, "gitlens.views.commits.setShowMergeCommitsOff": {"label": "Hide Merge Commits", "menus": {"gitlens/views/grouped/more": [{"when": "gitlens:views:scm:grouped:view == commits && view == gitlens.views.scm.grouped && !gitlens:views:commits:hideMergeCommits", "group": "5_gitlens", "order": 5}], "view/title": [{"when": "view == gitlens.views.commits && !gitlens:views:commits:hideMergeCommits", "group": "5_gitlens", "order": 5}, {"when": "view == gitlens.views.scm.grouped && gitlens:views:scm:grouped:view == commits && !gitlens:views:commits:hideMergeCommits", "group": "5_gitlens", "order": 5}]}}, "gitlens.views.commits.setShowMergeCommitsOn": {"label": "Show Merge Commits", "menus": {"gitlens/views/grouped/more": [{"when": "gitlens:views:scm:grouped:view == commits && view == gitlens.views.scm.grouped && gitlens:views:commits:hideMergeCommits", "group": "5_gitlens", "order": 5}], "view/title": [{"when": "view == gitlens.views.commits && gitlens:views:commits:hideMergeCommits", "group": "5_gitlens", "order": 5}, {"when": "view == gitlens.views.scm.grouped && gitlens:views:scm:grouped:view == commits && gitlens:views:commits:hideMergeCommits", "group": "5_gitlens", "order": 5}]}}, "gitlens.views.commits.setShowStashesOff": {"label": "<PERSON><PERSON> St<PERSON>", "menus": {"gitlens/views/grouped/more": [{"when": "gitlens:views:scm:grouped:view == commits && view == gitlens.views.scm.grouped && !gitlens:hasVirtualFolders && config.gitlens.views.commits.showStashes", "group": "5_gitlens", "order": 6}], "view/title": [{"when": "view == gitlens.views.commits && !gitlens:hasVirtualFolders && config.gitlens.views.commits.showStashes", "group": "5_gitlens", "order": 6}, {"when": "view == gitlens.views.scm.grouped && gitlens:views:scm:grouped:view == commits && !gitlens:hasVirtualFolders && config.gitlens.views.commits.showStashes", "group": "5_gitlens", "order": 6}]}}, "gitlens.views.commits.setShowStashesOn": {"label": "Show Stashes", "menus": {"gitlens/views/grouped/more": [{"when": "gitlens:views:scm:grouped:view == commits && view == gitlens.views.scm.grouped && !gitlens:hasVirtualFolders && !config.gitlens.views.commits.showStashes", "group": "5_gitlens", "order": 6}], "view/title": [{"when": "view == gitlens.views.commits && !gitlens:hasVirtualFolders && !config.gitlens.views.commits.showStashes", "group": "5_gitlens", "order": 6}, {"when": "view == gitlens.views.scm.grouped && gitlens:views:scm:grouped:view == commits && !gitlens:hasVirtualFolders && !config.gitlens.views.commits.showStashes", "group": "5_gitlens", "order": 6}]}}, "gitlens.views.commits.viewOptionsTitle": {"label": "Commits View Options", "enablement": "false", "menus": {"view/title": [{"when": "view == gitlens.views.scm.grouped && gitlens:views:scm:grouped:view == commits", "group": "1_gitlens_1", "order": 1}]}}, "gitlens.views.compareAncestryWithWorking": {"label": "Compare Working Tree to Common Base", "menus": {"view/item/context": [{"when": "viewItem =~ /gitlens:branch\\b(?!.*?\\b\\+current\\b)/ && !listMultiSelection && !gitlens:hasVirtualFolders", "group": "4_gitlens_compare", "order": 5}]}}, "gitlens.views.compareBranchWithHead": {"label": "Compare with HEAD", "icon": "$(compare-changes)", "menus": {"view/item/context": [{"when": "viewItem =~ /gitlens:branch\\b(?!.*?\\b\\+current\\b)/ && !gitlens:hasVirtualFolders", "group": "inline", "order": 97, "alt": "gitlens.views.compareWithWorking"}, {"when": "viewItem =~ /gitlens:branch\\b(?!.*?\\b\\+current\\b)/ && gitlens:hasVirtualFolders", "group": "inline", "order": 97}, {"when": "viewItem =~ /gitlens:branch\\b(?!.*?\\b\\+current\\b)/ && !listMultiSelection", "group": "4_gitlens_compare", "order": 2}]}}, "gitlens.views.compareFileWithSelected": {"label": "Compare with Selected", "menus": {"view/item/context": [{"when": "viewItem =~ /gitlens:file\\b/ && !listMultiSelection && gitlens:views:canCompare:file", "group": "4_gitlens_compare", "order": 98}]}}, "gitlens.views.compareWithHead": {"label": "Compare to/from HEAD", "icon": "$(compare-changes)", "menus": {"view/item/context": [{"when": "viewItem =~ /gitlens:(commit|stash|tag)\\b/", "group": "inline", "order": 97, "alt": "gitlens.views.compareWithWorking"}, {"when": "viewItem =~ /gitlens:(commit|stash|tag)\\b/ && !listMultiSelection", "group": "4_gitlens_compare", "order": 2}, {"when": "viewItem =~ /gitlens:file\\b(?=.*?\\b\\+committed\\b)/ && view =~ /^gitlens\\.views\\.(fileHistory|lineHistory)/ && !listMultiSelection", "group": "4_gitlens_compare", "order": 2}]}}, "gitlens.views.compareWithMergeBase": {"label": "Compare with Common Base", "menus": {"view/item/context": [{"when": "viewItem =~ /gitlens:branch\\b(?!.*?\\b\\+current\\b)/ && !listMultiSelection", "group": "4_gitlens_compare", "order": 3}]}}, "gitlens.views.compareWithSelected": {"label": "Compare with Selected", "menus": {"view/item/context": [{"when": "viewItem =~ /gitlens:(branch|commit|stash|tag)\\b/ && !listMultiSelection && gitlens:views:canCompare", "group": "4_gitlens_compare", "order": 98}]}}, "gitlens.views.compareWithUpstream": {"label": "Compare with Upstream", "menus": {"view/item/context": [{"when": "viewItem =~ /gitlens:branch\\b(?=.*?\\b\\+tracking\\b)/ && !listMultiSelection && !gitlens:hasVirtualFolders", "group": "4_gitlens_compare", "order": 1}]}}, "gitlens.views.compareWithWorking": {"label": "Compare Working Tree to Here", "icon": "$(gitlens-compare-ref-working)", "menus": {"view/item/context": [{"when": "viewItem =~ /gitlens:branch\\b(?=.*?\\b\\+current\\b)/ && !gitlens:hasVirtualFolders", "group": "inline", "order": 97}, {"when": "viewItem =~ /gitlens:(branch|commit|stash|tag)\\b/ && !listMultiSelection && !gitlens:hasVirtualFolders", "group": "4_gitlens_compare", "order": 4}, {"when": "viewItem =~ /gitlens:file\\b(?=.*?\\b\\+committed\\b)/ && view =~ /^gitlens\\.views\\.(fileHistory|lineHistory)/ && !listMultiSelection && !gitlens:hasVirtualFolders", "group": "4_gitlens_compare", "order": 3}]}}, "gitlens.views.continuePausedOperation": {"label": "Continue", "icon": "$(debug-continue)", "menus": {"view/item/context": [{"when": "viewItem =~ /gitlens:paused-operation:(cherry-pick|merge|rebase)\\b/ && !listMultiSelection", "group": "inline", "order": 1}, {"when": "viewItem =~ /gitlens:paused-operation:(cherry-pick|merge|rebase)\\b/ && !listMultiSelection", "group": "1_gitlens_actions", "order": 1}]}}, "gitlens.views.contributors.attach": {"label": "Group into GitLens View", "icon": "$(close)", "menus": {"view/title": [{"when": "view == gitlens.views.contributors", "group": "navigation", "order": 99}, {"when": "view == gitlens.views.contributors", "group": "9_git<PERSON>s", "order": 0}]}}, "gitlens.views.contributors.copy": {"label": "Copy", "keybindings": [{"key": "ctrl+c", "when": "focusedView == gitlens.views.contributors", "mac": "cmd+c"}]}, "gitlens.views.contributors.refresh": {"label": "Refresh", "icon": "$(refresh)", "menus": {"view/title": [{"when": "view == gitlens.views.contributors", "group": "navigation", "order": 98}]}}, "gitlens.views.contributors.setFilesLayoutToAuto": {"label": "View Files as Auto", "icon": "$(list-tree)", "enablement": "config.gitlens.views.contributors.files.layout != auto", "menus": {"gitlens/views/grouped/more": [{"when": "gitlens:views:scm:grouped:view == contributors && view == gitlens.views.scm.grouped", "group": "3_git<PERSON><PERSON>_", "order": 2}], "view/title": [{"when": "view == gitlens.views.contributors", "group": "3_git<PERSON><PERSON>_", "order": 2}, {"when": "view == gitlens.views.scm.grouped && gitlens:views:scm:grouped:view == contributors", "group": "3_git<PERSON><PERSON>_", "order": 2}]}}, "gitlens.views.contributors.setFilesLayoutToList": {"label": "View Files as List", "icon": "$(gitlens-list-auto)", "enablement": "config.gitlens.views.contributors.files.layout != list", "menus": {"gitlens/views/grouped/more": [{"when": "gitlens:views:scm:grouped:view == contributors && view == gitlens.views.scm.grouped", "group": "3_git<PERSON><PERSON>_", "order": 2}], "view/title": [{"when": "view == gitlens.views.contributors", "group": "3_git<PERSON><PERSON>_", "order": 2}, {"when": "view == gitlens.views.scm.grouped && gitlens:views:scm:grouped:view == contributors", "group": "3_git<PERSON><PERSON>_", "order": 2}]}}, "gitlens.views.contributors.setFilesLayoutToTree": {"label": "View Files as Tree", "icon": "$(list-flat)", "enablement": "config.gitlens.views.contributors.files.layout != tree", "menus": {"gitlens/views/grouped/more": [{"when": "gitlens:views:scm:grouped:view == contributors && view == gitlens.views.scm.grouped", "group": "3_git<PERSON><PERSON>_", "order": 2}], "view/title": [{"when": "view == gitlens.views.contributors", "group": "3_git<PERSON><PERSON>_", "order": 2}, {"when": "view == gitlens.views.scm.grouped && gitlens:views:scm:grouped:view == contributors", "group": "3_git<PERSON><PERSON>_", "order": 2}]}}, "gitlens.views.contributors.setShowAllBranchesOff": {"label": "View Current Branch Only", "menus": {"gitlens/views/grouped/more": [{"when": "gitlens:views:scm:grouped:view == contributors && view == gitlens.views.scm.grouped && config.gitlens.views.contributors.showAllBranches", "group": "3_git<PERSON>s", "order": 1}], "view/title": [{"when": "view == gitlens.views.contributors && config.gitlens.views.contributors.showAllBranches", "group": "3_git<PERSON>s", "order": 1}, {"when": "view == gitlens.views.scm.grouped && gitlens:views:scm:grouped:view == contributors && config.gitlens.views.contributors.showAllBranches", "group": "3_git<PERSON>s", "order": 1}]}}, "gitlens.views.contributors.setShowAllBranchesOn": {"label": "View All Branches", "menus": {"gitlens/views/grouped/more": [{"when": "gitlens:views:scm:grouped:view == contributors && view == gitlens.views.scm.grouped && !config.gitlens.views.contributors.showAllBranches", "group": "3_git<PERSON>s", "order": 1}], "view/title": [{"when": "view == gitlens.views.contributors && !config.gitlens.views.contributors.showAllBranches", "group": "3_git<PERSON>s", "order": 1}, {"when": "view == gitlens.views.scm.grouped && gitlens:views:scm:grouped:view == contributors && !config.gitlens.views.contributors.showAllBranches", "group": "3_git<PERSON>s", "order": 1}]}}, "gitlens.views.contributors.setShowAvatarsOff": {"label": "Hide Avatars", "menus": {"gitlens/views/grouped/more": [{"when": "gitlens:views:scm:grouped:view == contributors && view == gitlens.views.scm.grouped && config.gitlens.views.contributors.avatars", "group": "5_gitlens", "order": 1}], "view/title": [{"when": "view == gitlens.views.contributors && config.gitlens.views.contributors.avatars", "group": "5_gitlens", "order": 1}, {"when": "view == gitlens.views.scm.grouped && gitlens:views:scm:grouped:view == contributors && config.gitlens.views.contributors.avatars", "group": "5_gitlens", "order": 1}]}}, "gitlens.views.contributors.setShowAvatarsOn": {"label": "Show Avatars", "menus": {"gitlens/views/grouped/more": [{"when": "gitlens:views:scm:grouped:view == contributors && view == gitlens.views.scm.grouped && !config.gitlens.views.contributors.avatars", "group": "5_gitlens", "order": 1}], "view/title": [{"when": "view == gitlens.views.contributors && !config.gitlens.views.contributors.avatars", "group": "5_gitlens", "order": 1}, {"when": "view == gitlens.views.scm.grouped && gitlens:views:scm:grouped:view == contributors && !config.gitlens.views.contributors.avatars", "group": "5_gitlens", "order": 1}]}}, "gitlens.views.contributors.setShowMergeCommitsOff": {"label": "Hide Merge Commits", "menus": {"gitlens/views/grouped/more": [{"when": "gitlens:views:scm:grouped:view == contributors && view == gitlens.views.scm.grouped && !gitlens:views:contributors:hideMergeCommits", "group": "5_gitlens", "order": 3}], "view/title": [{"when": "view == gitlens.views.contributors && !gitlens:views:contributors:hideMergeCommits", "group": "5_gitlens", "order": 3}, {"when": "view == gitlens.views.scm.grouped && gitlens:views:scm:grouped:view == contributors && !gitlens:views:contributors:hideMergeCommits", "group": "5_gitlens", "order": 3}]}}, "gitlens.views.contributors.setShowMergeCommitsOn": {"label": "Show Merge Commits", "menus": {"gitlens/views/grouped/more": [{"when": "gitlens:views:scm:grouped:view == contributors && view == gitlens.views.scm.grouped && gitlens:views:contributors:hideMergeCommits", "group": "5_gitlens", "order": 3}], "view/title": [{"when": "view == gitlens.views.contributors && gitlens:views:contributors:hideMergeCommits", "group": "5_gitlens", "order": 3}, {"when": "view == gitlens.views.scm.grouped && gitlens:views:scm:grouped:view == contributors && gitlens:views:contributors:hideMergeCommits", "group": "5_gitlens", "order": 3}]}}, "gitlens.views.contributors.setShowStatisticsOff": {"label": "Hide Statistics", "menus": {"gitlens/views/grouped/more": [{"when": "gitlens:views:scm:grouped:view == contributors && view == gitlens.views.scm.grouped && config.gitlens.views.contributors.showStatistics", "group": "5_gitlens", "order": 4}], "view/title": [{"when": "view == gitlens.views.contributors && config.gitlens.views.contributors.showStatistics", "group": "5_gitlens", "order": 4}, {"when": "view == gitlens.views.scm.grouped && gitlens:views:scm:grouped:view == contributors && config.gitlens.views.contributors.showStatistics", "group": "5_gitlens", "order": 4}]}}, "gitlens.views.contributors.setShowStatisticsOn": {"label": "Show Statistics", "menus": {"gitlens/views/grouped/more": [{"when": "gitlens:views:scm:grouped:view == contributors && view == gitlens.views.scm.grouped && !config.gitlens.views.contributors.showStatistics", "group": "5_gitlens", "order": 4}], "view/title": [{"when": "view == gitlens.views.contributors && !config.gitlens.views.contributors.showStatistics", "group": "5_gitlens", "order": 4}, {"when": "view == gitlens.views.scm.grouped && gitlens:views:scm:grouped:view == contributors && !config.gitlens.views.contributors.showStatistics", "group": "5_gitlens", "order": 4}]}}, "gitlens.views.contributors.viewOptionsTitle": {"label": "Contributors View Options", "enablement": "false", "menus": {"view/title": [{"when": "view == gitlens.views.scm.grouped && gitlens:views:scm:grouped:view == contributors", "group": "1_gitlens_1", "order": 1}]}}, "gitlens.views.copy": {"label": "Copy", "icon": "$(copy)", "menus": {"view/item/context": [{"when": "viewItem =~ /gitlens:(?=(autolinked:item\\b|branch|commit|contributor|file(?!.*?\\b\\+(staged|unstaged))\\b|folder|history:line|launchpad:item|pullrequest|remote|repository|repo-folder|search:results|stash|tag|workspace|worktree)\\b)/", "group": "7_gitlens_cutcopypaste", "order": 1}, {"when": "viewItem =~ /gitlens:contributor\\b/", "group": "inline", "order": 98}]}}, "gitlens.views.copyAsMarkdown": {"label": "<PERSON><PERSON> as <PERSON><PERSON>", "icon": "$(copy)", "menus": {"view/item/context": [{"when": "viewItem =~ /gitlens:autolinked:item\\b/", "group": "7_gitlens_cutcopypaste", "order": 2}, {"when": "viewItem =~ /gitlens:contributor\\b/", "group": "7_gitlens_cutcopypaste", "order": 2}]}}, "gitlens.views.copyRemoteCommitUrl": {"label": "Copy Remote Commit URL", "icon": "$(copy)", "menus": {"gitlens/share": [{"when": "viewItem =~ /gitlens:(commit|file\\b(?=.*?\\b\\+committed\\b))/ && !listMultiSelection && gitlens:repos:withRemotes", "group": "2_git<PERSON>s", "order": 25}]}}, "gitlens.views.copyRemoteCommitUrl.multi": {"label": "Copy Remote Commit URLs", "icon": "$(copy)", "menus": {"gitlens/share": [{"when": "viewItem =~ /gitlens:(commit|file\\b(?=.*?\\b\\+committed\\b))/ && listMultiSelection && gitlens:repos:withRemotes", "group": "2_git<PERSON>s", "order": 25}]}}, "gitlens.views.copyUrl": {"label": "Copy URL", "icon": "$(copy)", "menus": {"view/item/context": [{"when": "viewItem =~ /gitlens:autolinked:item\\b/ && !listMultiSelection", "group": "7_gitlens_cutcopypaste", "order": 1}]}}, "gitlens.views.copyUrl.multi": {"label": "Copy URLs", "icon": "$(copy)", "menus": {"view/item/context": [{"when": "viewItem =~ /gitlens:autolinked:item\\b/ && listMultiSelection", "group": "7_gitlens_cutcopypaste", "order": 1}]}}, "gitlens.views.createBranch": {"label": "Create Branch...", "icon": "$(add)", "enablement": "!operationInProgress", "menus": {"gitlens/commit/file/commit": [{"when": "viewItem =~ /gitlens:file\\b(?=.*?\\b\\+committed\\b)/ && !gitlens:readonly && !gitlens:untrusted && !gitlens:hasVirtualFolders", "group": "1_gitlens_secondary_actions", "order": 1}], "view/item/context": [{"when": "viewItem =~ /gitlens:branch\\b(?!.*?\\b\\+closed\\b)/ && !listMultiSelection && !gitlens:readonly && !gitlens:untrusted && !gitlens:hasVirtualFolders", "group": "1_gitlens_actions_3", "order": 7}, {"when": "viewItem =~ /gitlens:branches\\b(?!.*?\\b\\+closed\\b)/ && !gitlens:readonly && !gitlens:untrusted && !gitlens:hasVirtualFolders", "group": "inline", "order": 11}, {"when": "viewItem =~ /gitlens:branches\\b(?!.*?\\b\\+closed\\b)/ && !listMultiSelection && !gitlens:readonly && !gitlens:untrusted && !gitlens:hasVirtualFolders", "group": "1_gitlens_actions", "order": 2}, {"when": "viewItem =~ /gitlens:commit\\b/ && !listMultiSelection && !gitlens:readonly && !gitlens:untrusted && !gitlens:hasVirtualFolders", "group": "1_gitlens_actions_1", "order": 1}, {"when": "viewItem =~ /gitlens:status:upstream/ && !listMultiSelection && !gitlens:readonly && !gitlens:untrusted && !gitlens:hasVirtualFolders", "group": "1_gitlens_secondary_actions", "order": 1}, {"when": "viewItem =~ /gitlens:tag\\b/ && !listMultiSelection && !gitlens:readonly && !gitlens:untrusted && !gitlens:hasVirtualFolders", "group": "1_gitlens_actions_3", "order": 3}]}}, "gitlens.views.createPullRequest": {"label": "Create Pull Request...", "icon": "$(git-pull-request-create)", "menus": {"view/item/context": [{"when": "viewItem =~ /gitlens:branch\\b(?=.*?\\b\\+(tracking|remote)\\b)(?!.*?\\b\\+closed\\b)/ && gitlens:action:createPullRequest && gitlens:repos:withRemotes", "group": "inline", "order": 9}, {"when": "viewItem =~ /gitlens:branch\\b(?=.*?\\b\\+(tracking|remote)\\b)(?!.*?\\b\\+closed\\b)/ && !listMultiSelection && gitlens:action:createPullRequest && gitlens:repos:withRemotes", "group": "1_gitlens_actions_3", "order": 10}, {"when": "viewItem =~ /gitlens:status:upstream:(?!(missing|none))/ && !listMultiSelection && !gitlens:readonly && !gitlens:untrusted && !gitlens:hasVirtualFolders && gitlens:action:createPullRequest && gitlens:repos:withRemotes", "group": "1_gitlens_secondary_actions", "order": 3}, {"when": "viewItem =~ /gitlens:status:upstream:same/ && !gitlens:readonly && !gitlens:untrusted && !gitlens:hasVirtualFolders && gitlens:action:createPullRequest && gitlens:repos:withRemotes", "group": "inline", "order": 1}]}}, "gitlens.views.createTag": {"label": "Create Tag...", "icon": "$(add)", "enablement": "!operationInProgress", "menus": {"gitlens/commit/file/commit": [{"when": "viewItem =~ /gitlens:file\\b(?=.*?\\b\\+committed\\b)/ && !gitlens:readonly && !gitlens:untrusted && !gitlens:hasVirtualFolders", "group": "1_gitlens_secondary_actions", "order": 4}], "view/item/context": [{"when": "viewItem =~ /gitlens:branch\\b(?!.*?\\b\\+closed\\b)/ && !listMultiSelection && !gitlens:readonly && !gitlens:untrusted && !gitlens:hasVirtualFolders", "group": "1_gitlens_actions_3", "order": 8}, {"when": "viewItem =~ /gitlens:commit\\b/ && !listMultiSelection && !gitlens:readonly && !gitlens:untrusted && !gitlens:hasVirtualFolders", "group": "1_gitlens_actions_1", "order": 4}, {"when": "viewItem =~ /gitlens:status:upstream/ && !listMultiSelection && !gitlens:readonly && !gitlens:untrusted && !gitlens:hasVirtualFolders", "group": "1_gitlens_secondary_actions", "order": 2}, {"when": "viewItem =~ /gitlens:tags\\b/ && !gitlens:readonly && !gitlens:untrusted && !gitlens:hasVirtualFolders", "group": "inline", "order": 1}, {"when": "viewItem =~ /gitlens:tags\\b/ && !listMultiSelection && !gitlens:readonly && !gitlens:untrusted && !gitlens:hasVirtualFolders", "group": "1_gitlens_actions", "order": 1}]}}, "gitlens.views.createWorktree": {"label": "Create Worktree...", "icon": "$(add)", "enablement": "!operationInProgress", "menus": {"view/item/context": [{"when": "viewItem =~ /gitlens:branch\\b(?!.*?\\b\\+closed\\b)/ && !listMultiSelection && !gitlens:readonly && !gitlens:untrusted && !gitlens:hasVirtualFolders", "group": "1_gitlens_actions_3", "order": 9}, {"when": "viewItem =~ /gitlens:worktrees\\b/ && !gitlens:readonly && !gitlens:untrusted && !gitlens:hasVirtualFolders", "group": "inline", "order": 1}, {"when": "viewItem =~ /gitlens:worktrees\\b/ && !listMultiSelection && !gitlens:readonly && !gitlens:untrusted && !gitlens:hasVirtualFolders", "group": "1_gitlens_actions", "order": 1}]}}, "gitlens.views.deleteBranch": {"label": "Delete Branch...", "icon": "$(trash)", "enablement": "!operationInProgress", "menus": {"view/item/context": [{"when": "viewItem =~ /gitlens:branch\\b(?!.*?\\b\\+current\\b)(?!.*?\\b\\+closed\\b)/ && !listMultiSelection && !gitlens:readonly && !gitlens:untrusted && !gitlens:hasVirtualFolders", "group": "1_gitlens_actions_2", "order": 3}]}}, "gitlens.views.deleteBranch.multi": {"label": "Delete Branches...", "icon": "$(trash)", "enablement": "!operationInProgress", "menus": {"view/item/context": [{"when": "viewItem =~ /gitlens:branch\\b(?!.*?\\b\\+current\\b)(?!.*?\\b\\+closed\\b)/ && listMultiSelection && !gitlens:readonly && !gitlens:untrusted && !gitlens:hasVirtualFolders", "group": "1_gitlens_actions_2", "order": 3}]}}, "gitlens.views.deleteTag": {"label": "Delete Tag...", "icon": "$(trash)", "enablement": "!operationInProgress", "menus": {"view/item/context": [{"when": "viewItem == gitlens:tag && !listMultiSelection && !gitlens:readonly && !gitlens:untrusted && !gitlens:hasVirtualFolders", "group": "1_gitlens_actions", "order": 2}]}}, "gitlens.views.deleteTag.multi": {"label": "Delete Tags...", "icon": "$(trash)", "enablement": "!operationInProgress", "menus": {"view/item/context": [{"when": "viewItem == gitlens:tag && listMultiSelection && !gitlens:readonly && !gitlens:untrusted && !gitlens:hasVirtualFolders", "group": "1_gitlens_actions", "order": 2}]}}, "gitlens.views.deleteWorktree": {"label": "Delete Worktree...", "icon": "$(trash)", "enablement": "!operationInProgress", "menus": {"view/item/context": [{"when": "viewItem =~ /gitlens:worktree\\b(?!.*?\\b\\+(active|default)\\b)/ && !listMultiSelection && !gitlens:readonly", "group": "6_gitlens_actions", "order": 1}]}}, "gitlens.views.deleteWorktree.multi": {"label": "Delete Worktrees...", "icon": "$(trash)", "enablement": "!operationInProgress", "menus": {"view/item/context": [{"when": "viewItem =~ /gitlens:worktree\\b(?!.*?\\b\\+(active|default)\\b)/ && listMultiSelection && !gitlens:readonly", "group": "6_gitlens_actions", "order": 1}]}}, "gitlens.views.dismissNode": {"label": "<PERSON><PERSON><PERSON>", "icon": "$(close)", "menus": {"view/item/context": [{"when": "viewItem =~ /gitlens:(compare:picker|(compare|search):results(?!:)\\b)\\b(?!:(commits|files))/", "group": "inline", "order": 99}, {"when": "viewItem =~ /gitlens:(compare:picker:ref|(compare|search):results(?!:)\\b)\\b(?!:(commits|files))/ && !listMultiSelection", "group": "1_gitlens_actions", "order": 98}]}}, "gitlens.views.draft.open": {"label": "Open", "icon": "$(eye)", "menus": {"view/item/context": [{"when": "viewItem =~ /gitlens:draft\\b/ && gitlens:plus", "group": "1_gitlens_actions", "order": 1}]}}, "gitlens.views.draft.openOnWeb": {"label": "Open on gitkraken.dev", "icon": "$(globe)", "menus": {"view/item/context": [{"when": "viewItem =~ /gitlens:draft\\b/ && gitlens:plus", "group": "inline", "order": 99}, {"when": "viewItem =~ /gitlens:draft\\b/ && gitlens:plus", "group": "1_gitlens_actions", "order": 2}]}}, "gitlens.views.drafts.copy": {"label": "Copy", "keybindings": [{"key": "ctrl+c", "when": "focusedView == gitlens.views.drafts", "mac": "cmd+c"}]}, "gitlens.views.drafts.create": {"label": "Create Cloud Patch...", "icon": "$(add)", "menus": {"view/title": [{"when": "view == gitlens.views.drafts && gitlens:plus", "group": "navigation", "order": 1}]}}, "gitlens.views.drafts.delete": {"label": "Delete Cloud Patch...", "icon": "$(trash)", "menus": {"view/item/context": [{"when": "viewItem =~ /gitlens:draft\\b(?=.*?\\b\\+mine\\b)/ && gitlens:plus", "group": "6_gitlens_actions", "order": 1}]}}, "gitlens.views.drafts.info": {"label": "Learn about Cloud Patches...", "icon": "$(info)", "menus": {"view/title": [{"when": "view == gitlens.views.drafts", "group": "8_info", "order": 1}]}}, "gitlens.views.drafts.refresh": {"label": "Refresh", "icon": "$(refresh)", "menus": {"view/title": [{"when": "view == gitlens.views.drafts", "group": "navigation", "order": 99}]}}, "gitlens.views.drafts.setShowAvatarsOff": {"label": "Hide Avatars", "menus": {"view/title": [{"when": "view == gitlens.views.drafts && config.gitlens.views.drafts.avatars", "group": "5_gitlens", "order": 1}]}}, "gitlens.views.drafts.setShowAvatarsOn": {"label": "Show Avatars", "menus": {"view/title": [{"when": "view == gitlens.views.drafts && !config.gitlens.views.drafts.avatars", "group": "5_gitlens", "order": 1}]}}, "gitlens.views.editNode": {"label": "Edit...", "icon": "$(edit)", "menus": {"view/item/context": [{"when": "viewItem =~ /gitlens:compare:branch\\b(?=.*?\\b\\+comparing\\b)/", "group": "inline", "order": 96}, {"when": "viewItem =~ /gitlens:compare:branch\\b(?=.*?\\b\\+comparing\\b)/ && !listMultiSelection", "group": "1_gitlens", "order": 2}, {"when": "viewItem =~ /gitlens:search:results(?!:)\\b/", "group": "inline", "order": 1}, {"when": "viewItem =~ /gitlens:search:results(?!:)\\b/ && !listMultiSelection", "group": "1_gitlens_actions", "order": 1}]}}, "gitlens.views.expandNode": {"label": "Expand", "menus": {"view/item/context": [{"when": "viewItem =~ /gitlens:(branch|commit|compare|contributor|folder|grouping|launchpad:item|pseudo:folder|pullrequest|remote|results|search|stash|status|tag|worktree)\\b/ && !listMultiSelection", "group": "9_git<PERSON>s", "order": 2}]}}, "gitlens.views.fetch": {"label": "<PERSON>tch", "icon": "$(repo-fetch)", "enablement": "!operationInProgress", "menus": {"view/item/context": [{"when": "viewItem =~ /gitlens:branch\\b(?=.*?\\b\\+tracking\\b)(?!.*?\\b\\+(ahead|behind|closed)\\b)/ && !gitlens:readonly && !gitlens:untrusted && !gitlens:hasVirtualFolders && gitlens:repos:withRemotes", "group": "inline", "order": 8, "alt": "gitlens.views.pull"}, {"when": "viewItem =~ /gitlens:branch\\b(?=.*?\\b\\+remote\\b)(?!.*?\\b\\+(ahead|behind|closed)\\b)/ && !gitlens:readonly && !gitlens:untrusted && !gitlens:hasVirtualFolders && gitlens:repos:withRemotes", "group": "inline", "order": 8}, {"when": "viewItem =~ /gitlens:branch\\b(?=.*?\\b\\+(remote|tracking)\\b)(?!.*?\\b\\+closed\\b)/ && !listMultiSelection && !gitlens:readonly && !gitlens:untrusted && !gitlens:hasVirtualFolders && gitlens:repos:withRemotes", "group": "1_gitlens_actions", "order": 3}, {"when": "viewItem =~ /gitlens:remote\\b/ && !gitlens:readonly && !gitlens:untrusted && !gitlens:hasVirtualFolders", "group": "inline", "order": 97}, {"when": "viewItem =~ /gitlens:remote\\b/ && !listMultiSelection && !gitlens:readonly && !gitlens:untrusted && !gitlens:hasVirtualFolders", "group": "1_gitlens_actions", "order": 1}, {"when": "viewItem =~ /gitlens:repo-folder\\b(?!.*?\\b\\+ahead\\b)(?!.*?\\b\\+behind\\b)/ && !gitlens:readonly && !gitlens:untrusted && !gitlens:hasVirtualFolders && gitlens:repos:withRemotes", "group": "inline", "order": 98}, {"when": "viewItem =~ /gitlens:repo-folder\\b/ && !listMultiSelection && !gitlens:readonly && !gitlens:untrusted && !gitlens:hasVirtualFolders && gitlens:repos:withRemotes", "group": "1_gitlens_actions", "order": 1}, {"when": "viewItem =~ /gitlens:repository\\b(?!.*?\\b\\+closed\\b)/ && !gitlens:readonly && !gitlens:untrusted && !gitlens:hasVirtualFolders && gitlens:repos:withRemotes", "group": "inline", "order": 98}, {"when": "viewItem =~ /gitlens:repository\\b(?!.*?\\b\\+closed\\b)/ && !listMultiSelection && !gitlens:readonly && !gitlens:untrusted && !gitlens:hasVirtualFolders && gitlens:repos:withRemotes", "group": "1_gitlens_actions", "order": 1}, {"when": "viewItem =~ /gitlens:status(\\-branch)?:upstream:(?!(missing|none))/ && !gitlens:readonly && !gitlens:untrusted && !gitlens:hasVirtualFolders && gitlens:repos:withRemotes", "group": "inline", "order": 98}, {"when": "viewItem =~ /gitlens:status:upstream:(?!(missing|none))/ && !listMultiSelection && !gitlens:readonly && !gitlens:untrusted && !gitlens:hasVirtualFolders && gitlens:repos:withRemotes", "group": "1_gitlens_actions", "order": 3}]}}, "gitlens.views.fileHistory.attach": {"label": "Group into GitLens View", "icon": "$(close)", "menus": {"view/title": [{"when": "view == gitlens.views.fileHistory", "group": "navigation", "order": 99}, {"when": "view == gitlens.views.fileHistory", "group": "9_git<PERSON>s", "order": 0}]}}, "gitlens.views.fileHistory.changeBase": {"label": "Change Base...", "icon": "$(history)", "menus": {"gitlens/views/grouped/fileHistory": [{"when": "gitlens:views:scm:grouped:view == fileHistory && !gitlens:hasVirtualFolders", "group": "2_git<PERSON>s", "order": 0}], "view/title": [{"when": "view == gitlens.views.fileHistory", "group": "1_gitlens", "order": 0}]}}, "gitlens.views.fileHistory.copy": {"label": "Copy", "keybindings": [{"key": "ctrl+c", "when": "focusedView == gitlens.views.fileHistory", "mac": "cmd+c"}]}, "gitlens.views.fileHistory.refresh": {"label": "Refresh", "icon": "$(refresh)", "menus": {"view/title": [{"when": "view == gitlens.views.fileHistory", "group": "navigation", "order": 99}]}}, "gitlens.views.fileHistory.setCursorFollowingOff": {"label": "View File History", "icon": "$(list-selection)", "enablement": "gitlens:views:fileHistory:editorFollowing || gitlens:views:fileHistory:cursorFollowing", "menus": {"gitlens/views/grouped/fileHistory": [{"when": "gitlens:views:scm:grouped:view == fileHistory && !gitlens:hasVirtualFolders && gitlens:views:fileHistory:cursorFollowing", "group": "1_gitlens", "order": 1}], "view/title": [{"when": "view == gitlens.views.fileHistory && !gitlens:hasVirtualFolders && gitlens:views:fileHistory:cursorFollowing", "group": "navigation", "order": 11}]}}, "gitlens.views.fileHistory.setCursorFollowingOn": {"label": "View Line History", "icon": "$(file)", "enablement": "gitlens:views:fileHistory:editorFollowing", "menus": {"gitlens/views/grouped/fileHistory": [{"when": "gitlens:views:scm:grouped:view == fileHistory && !gitlens:hasVirtualFolders && !gitlens:views:fileHistory:cursorFollowing", "group": "1_gitlens", "order": 1}], "view/title": [{"when": "view == gitlens.views.fileHistory && !gitlens:hasVirtualFolders && !gitlens:views:fileHistory:cursorFollowing", "group": "navigation", "order": 11}]}}, "gitlens.views.fileHistory.setEditorFollowingOff": {"label": "Pin the Current History", "icon": "$(pin)", "menus": {"gitlens/views/grouped/fileHistory": [{"when": "gitlens:views:scm:grouped:view == fileHistory && gitlens:views:fileHistory:canPin && gitlens:views:fileHistory:editorFollowing", "group": "1_gitlens", "order": 0}], "view/title": [{"when": "view == gitlens.views.fileHistory && gitlens:views:fileHistory:canPin && gitlens:views:fileHistory:editorFollowing", "group": "navigation", "order": 10}]}}, "gitlens.views.fileHistory.setEditorFollowingOn": {"label": "Unpin the Current History", "icon": "$(pinned)", "menus": {"gitlens/views/grouped/fileHistory": [{"when": "gitlens:views:scm:grouped:view == fileHistory && !gitlens:views:fileHistory:editorFollowing", "group": "1_gitlens", "order": 0}], "view/title": [{"when": "view == gitlens.views.fileHistory && !gitlens:views:fileHistory:editorFollowing", "group": "navigation", "order": 10}]}}, "gitlens.views.fileHistory.setModeCommits": {"label": "Show Commits", "icon": "$(git-commit)", "enablement": "!gitlens:views:fileHistory:cursorFollowing", "commandPalette": true, "menus": {"gitlens/views/grouped/fileHistory": [{"when": "gitlens:views:scm:grouped:view == fileHistory && !gitlens:views:fileHistory:cursorFollowing && gitlens:views:fileHistory:mode == contributors", "group": "3_git<PERSON>s", "order": 10}], "view/title": [{"when": "view == gitlens.views.fileHistory && gitlens:views:fileHistory:mode == contributors", "group": "navigation", "order": 12}]}}, "gitlens.views.fileHistory.setModeContributors": {"label": "Show Contributors", "icon": "$(organization)", "enablement": "!gitlens:views:fileHistory:cursorFollowing", "commandPalette": true, "menus": {"gitlens/views/grouped/fileHistory": [{"when": "gitlens:views:scm:grouped:view == fileHistory && !gitlens:views:fileHistory:cursorFollowing && gitlens:views:fileHistory:mode == commits", "group": "3_git<PERSON>s", "order": 10}], "view/title": [{"when": "view == gitlens.views.fileHistory && gitlens:views:fileHistory:mode == commits", "group": "navigation", "order": 12}]}}, "gitlens.views.fileHistory.setRenameFollowingOff": {"label": "Don't Follow <PERSON><PERSON>", "menus": {"gitlens/views/grouped/fileHistory": [{"when": "gitlens:views:scm:grouped:view == fileHistory && !gitlens:views:fileHistory:cursorFollowing && config.gitlens.advanced.fileHistoryFollowsRenames", "group": "3_git<PERSON>s", "order": 1}], "view/title": [{"when": "view == gitlens.views.fileHistory && !gitlens:views:fileHistory:cursorFollowing && config.gitlens.advanced.fileHistoryFollowsRenames", "group": "3_git<PERSON>s", "order": 1}]}}, "gitlens.views.fileHistory.setRenameFollowingOn": {"label": "Follow <PERSON><PERSON><PERSON>", "menus": {"gitlens/views/grouped/fileHistory": [{"when": "gitlens:views:scm:grouped:view == fileHistory && !gitlens:views:fileHistory:cursorFollowing && !config.gitlens.advanced.fileHistoryFollowsRenames", "group": "3_git<PERSON>s", "order": 1}], "view/title": [{"when": "view == gitlens.views.fileHistory && !gitlens:views:fileHistory:cursorFollowing && !config.gitlens.advanced.fileHistoryFollowsRenames", "group": "3_git<PERSON>s", "order": 1}]}}, "gitlens.views.fileHistory.setShowAllBranchesOff": {"label": "View History for Current Branch Only", "menus": {"gitlens/views/grouped/fileHistory": [{"when": "gitlens:views:scm:grouped:view == fileHistory && !gitlens:views:fileHistory:cursorFollowing && config.gitlens.advanced.fileHistoryShowAllBranches", "group": "3_git<PERSON>s", "order": 3}], "view/title": [{"when": "view == gitlens.views.fileHistory && !gitlens:views:fileHistory:cursorFollowing && config.gitlens.advanced.fileHistoryShowAllBranches", "group": "3_git<PERSON>s", "order": 3}]}}, "gitlens.views.fileHistory.setShowAllBranchesOn": {"label": "View History for All Branches", "menus": {"gitlens/views/grouped/fileHistory": [{"when": "gitlens:views:scm:grouped:view == fileHistory && !gitlens:views:fileHistory:cursorFollowing && !config.gitlens.advanced.fileHistoryShowAllBranches", "group": "3_git<PERSON>s", "order": 3}], "view/title": [{"when": "view == gitlens.views.fileHistory && !gitlens:views:fileHistory:cursorFollowing && !config.gitlens.advanced.fileHistoryShowAllBranches", "group": "3_git<PERSON>s", "order": 3}]}}, "gitlens.views.fileHistory.setShowAvatarsOff": {"label": "Hide Avatars", "menus": {"gitlens/views/grouped/more": [{"when": "gitlens:views:scm:grouped:view == fileHistory && config.gitlens.views.fileHistory.avatars", "group": "5_gitlens", "order": 1}], "view/title": [{"when": "view == gitlens.views.fileHistory && config.gitlens.views.fileHistory.avatars", "group": "5_gitlens", "order": 1}, {"when": "view == gitlens.views.scm.grouped && gitlens:views:scm:grouped:view == fileHistory && config.gitlens.views.fileHistory.avatars", "group": "5_gitlens", "order": 1}]}}, "gitlens.views.fileHistory.setShowAvatarsOn": {"label": "Show Avatars", "menus": {"gitlens/views/grouped/more": [{"when": "gitlens:views:scm:grouped:view == fileHistory && !config.gitlens.views.fileHistory.avatars", "group": "5_gitlens", "order": 1}], "view/title": [{"when": "view == gitlens.views.fileHistory && !config.gitlens.views.fileHistory.avatars", "group": "5_gitlens", "order": 1}, {"when": "view == gitlens.views.scm.grouped && gitlens:views:scm:grouped:view == fileHistory && !config.gitlens.views.fileHistory.avatars", "group": "5_gitlens", "order": 1}]}}, "gitlens.views.fileHistory.setShowMergeCommitsOff": {"label": "Hide Merge Commits", "menus": {"gitlens/views/grouped/fileHistory": [{"when": "gitlens:views:scm:grouped:view == fileHistory && !gitlens:views:fileHistory:cursorFollowing && config.gitlens.advanced.fileHistoryShowMergeCommits", "group": "3_git<PERSON>s", "order": 2}], "view/title": [{"when": "view == gitlens.views.fileHistory && !gitlens:views:fileHistory:cursorFollowing && config.gitlens.advanced.fileHistoryShowMergeCommits", "group": "3_git<PERSON>s", "order": 2}]}}, "gitlens.views.fileHistory.setShowMergeCommitsOn": {"label": "Show Merge Commits", "menus": {"gitlens/views/grouped/fileHistory": [{"when": "gitlens:views:scm:grouped:view == fileHistory && !gitlens:views:fileHistory:cursorFollowing && !config.gitlens.advanced.fileHistoryShowMergeCommits", "group": "3_git<PERSON>s", "order": 2}], "view/title": [{"when": "view == gitlens.views.fileHistory && !gitlens:views:fileHistory:cursorFollowing && !config.gitlens.advanced.fileHistoryShowMergeCommits", "group": "3_git<PERSON>s", "order": 2}]}}, "gitlens.views.fileHistory.viewOptionsTitle": {"label": "File History View Options", "enablement": "false", "menus": {"view/title": [{"when": "view == gitlens.views.scm.grouped && gitlens:views:scm:grouped:view == fileHistory", "group": "1_gitlens_1", "order": 1}]}}, "gitlens.views.graph.openInTab": {"label": "Open in Editor", "icon": "$(link-external)", "menus": {"view/title": [{"when": "view == gitlens.views.graph", "group": "navigation", "order": -100}]}}, "gitlens.views.graph.refresh": {"label": "Refresh", "icon": "$(refresh)", "menus": {"view/title": [{"when": "view == gitlens.views.graph", "group": "navigation", "order": -99}]}}, "gitlens.views.graphDetails.refresh": {"label": "Refresh", "icon": "$(refresh)", "menus": {"view/title": [{"when": "view == gitlens.views.graphDetails", "group": "navigation", "order": 99}]}}, "gitlens.views.highlightChanges": {"label": "Highlight All Changes Since Before this Commit", "menus": {"gitlens/commit/file/changes": [{"when": "viewItem =~ /gitlens:file\\b((?=.*?\\b\\+(committed|stashed)\\b)|:results)/", "group": "2_git<PERSON>s", "order": 1}]}}, "gitlens.views.highlightRevisionChanges": {"label": "Highlight Changes from this Commit", "menus": {"gitlens/commit/file/changes": [{"when": "viewItem =~ /gitlens:file\\b((?=.*?\\b\\+committed\\b)|:results)/", "group": "2_git<PERSON>s", "order": 2}]}}, "gitlens.views.home.disablePreview": {"label": "Revert to Old Home View", "icon": "$(discard)", "menus": {"view/title": [{"when": "view == gitlens.views.home && config.gitlens.home.preview.enabled", "group": "navigation", "order": 97}]}}, "gitlens.views.home.discussions": {"label": "GitHub Discussions", "icon": "$(comment-discussion)", "menus": {"view/title": [{"when": "view == gitlens.views.home", "group": "2_git<PERSON>s", "order": 99}]}}, "gitlens.views.home.enablePreview": {"label": "Switch to New Home View", "icon": "$(redo)", "menus": {"view/title": [{"when": "view == gitlens.views.home && !config.gitlens.home.preview.enabled", "group": "navigation", "order": 97}]}}, "gitlens.views.home.help": {"label": "Help Center", "icon": "$(question)", "menus": {"view/title": [{"when": "view == gitlens.views.home", "group": "1_gitlens", "order": 97}]}}, "gitlens.views.home.info": {"label": "Learn about Home View", "icon": "$(question)", "menus": {"view/title": [{"when": "view == gitlens.views.home", "group": "navigation", "order": 98}]}}, "gitlens.views.home.issues": {"label": "GitHub Issues", "icon": "$(feedback)", "menus": {"view/title": [{"when": "view == gitlens.views.home", "group": "2_git<PERSON>s", "order": 98}]}}, "gitlens.views.home.previewFeedback": {"label": "New Home View Feedback", "icon": "$(feedback)", "menus": {"view/title": [{"when": "view == gitlens.views.home", "group": "navigation", "order": 96}]}}, "gitlens.views.home.refresh": {"label": "Refresh", "icon": "$(refresh)", "menus": {"view/title": [{"when": "view == gitlens.views.home", "group": "navigation", "order": 99}]}}, "gitlens.views.home.whatsNew": {"label": "What's New (Release Notes)", "icon": "$(megaphone)", "menus": {"view/title": [{"when": "view == gitlens.views.home", "group": "navigation", "order": 1}]}}, "gitlens.views.launchpad.attach": {"label": "Group into GitLens View", "icon": "$(close)", "menus": {"view/title": [{"when": "view == gitlens.views.launchpad", "group": "navigation", "order": 99}, {"when": "view == gitlens.views.launchpad", "group": "9_git<PERSON>s", "order": 0}]}}, "gitlens.views.launchpad.copy": {"label": "Copy"}, "gitlens.views.launchpad.info": {"label": "Learn about Launchpad...", "icon": "$(info)", "menus": {"gitlens/views/grouped/launchpad": [{"when": "gitlens:views:scm:grouped:view == launchpad", "group": "2_gitlens_actions", "order": 1}], "view/title": [{"when": "view == gitlens.views.launchpad", "group": "8_info", "order": 1}]}}, "gitlens.views.launchpad.refresh": {"label": "Refresh", "icon": "$(refresh)", "menus": {"view/title": [{"when": "view == gitlens.views.launchpad", "group": "navigation", "order": 98}]}}, "gitlens.views.launchpad.setFilesLayoutToAuto": {"label": "View Files as Auto", "icon": "$(list-tree)", "enablement": "config.gitlens.views.launchpad.files.layout != auto", "menus": {"gitlens/views/grouped/more": [{"when": "gitlens:views:scm:grouped:view == launchpad && view == gitlens.views.scm.grouped", "group": "3_git<PERSON><PERSON>_", "order": 1}], "view/title": [{"when": "view == gitlens.views.launchpad && config.gitlens.views.launchpad.files.layout == tree", "group": "navigation", "order": 50}, {"when": "view == gitlens.views.scm.grouped && gitlens:views:scm:grouped:view == launchpad", "group": "3_git<PERSON><PERSON>_", "order": 1}]}}, "gitlens.views.launchpad.setFilesLayoutToList": {"label": "View Files as List", "icon": "$(gitlens-list-auto)", "enablement": "config.gitlens.views.launchpad.files.layout != list", "menus": {"gitlens/views/grouped/more": [{"when": "gitlens:views:scm:grouped:view == launchpad && view == gitlens.views.scm.grouped", "group": "3_git<PERSON><PERSON>_", "order": 1}], "view/title": [{"when": "view == gitlens.views.launchpad && config.gitlens.views.launchpad.files.layout == auto", "group": "navigation", "order": 50}, {"when": "view == gitlens.views.scm.grouped && gitlens:views:scm:grouped:view == launchpad", "group": "3_git<PERSON><PERSON>_", "order": 1}]}}, "gitlens.views.launchpad.setFilesLayoutToTree": {"label": "View Files as Tree", "icon": "$(list-flat)", "enablement": "config.gitlens.views.launchpad.files.layout != tree", "menus": {"gitlens/views/grouped/more": [{"when": "gitlens:views:scm:grouped:view == launchpad && view == gitlens.views.scm.grouped", "group": "3_git<PERSON><PERSON>_", "order": 1}], "view/title": [{"when": "view == gitlens.views.launchpad && config.gitlens.views.launchpad.files.layout == list", "group": "navigation", "order": 50}, {"when": "view == gitlens.views.scm.grouped && gitlens:views:scm:grouped:view == launchpad", "group": "3_git<PERSON><PERSON>_", "order": 1}]}}, "gitlens.views.launchpad.setShowAvatarsOff": {"label": "Hide Avatars", "menus": {"gitlens/views/grouped/more": [{"when": "gitlens:views:scm:grouped:view == launchpad && view == gitlens.views.scm.grouped && config.gitlens.views.launchpad.avatars", "group": "5_gitlens", "order": 1}], "view/title": [{"when": "view == gitlens.views.launchpad && config.gitlens.views.launchpad.avatars", "group": "5_gitlens", "order": 1}, {"when": "view == gitlens.views.scm.grouped && gitlens:views:scm:grouped:view == launchpad && config.gitlens.views.launchpad.avatars", "group": "5_gitlens", "order": 1}]}}, "gitlens.views.launchpad.setShowAvatarsOn": {"label": "Show Avatars", "menus": {"gitlens/views/grouped/more": [{"when": "gitlens:views:scm:grouped:view == launchpad && view == gitlens.views.scm.grouped && !config.gitlens.views.launchpad.avatars", "group": "5_gitlens", "order": 1}], "view/title": [{"when": "view == gitlens.views.launchpad && !config.gitlens.views.launchpad.avatars", "group": "5_gitlens", "order": 1}, {"when": "view == gitlens.views.scm.grouped && gitlens:views:scm:grouped:view == launchpad && !config.gitlens.views.launchpad.avatars", "group": "5_gitlens", "order": 1}]}}, "gitlens.views.launchpad.viewOptionsTitle": {"label": "Launchpad View Options", "enablement": "false", "menus": {"view/title": [{"when": "view == gitlens.views.scm.grouped && gitlens:views:scm:grouped:view == launchpad", "group": "1_gitlens_1", "order": 1}]}}, "gitlens.views.lineHistory.changeBase": {"label": "Change Base...", "icon": "$(history)", "menus": {"view/title": [{"when": "view == gitlens.views.lineHistory", "group": "1_gitlens", "order": 0}]}}, "gitlens.views.lineHistory.copy": {"label": "Copy", "keybindings": [{"key": "ctrl+c", "when": "focusedView == gitlens.views.lineHistory", "mac": "cmd+c"}]}, "gitlens.views.lineHistory.refresh": {"label": "Refresh", "icon": "$(refresh)", "menus": {"view/title": [{"when": "view == gitlens.views.lineHistory", "group": "navigation", "order": 99}]}}, "gitlens.views.lineHistory.setEditorFollowingOff": {"label": "Pin the Current History", "icon": "$(pin)", "menus": {"view/title": [{"when": "view == gitlens.views.lineHistory && gitlens:views:lineHistory:editorFollowing", "group": "navigation", "order": 10}]}}, "gitlens.views.lineHistory.setEditorFollowingOn": {"label": "Unpin the Current History", "icon": "$(pinned)", "menus": {"view/title": [{"when": "view == gitlens.views.lineHistory && !gitlens:views:lineHistory:editorFollowing", "group": "navigation", "order": 10}]}}, "gitlens.views.lineHistory.setShowAvatarsOff": {"label": "Hide Avatars", "menus": {"view/title": [{"when": "view == gitlens.views.lineHistory && config.gitlens.views.lineHistory.avatars", "group": "5_gitlens", "order": 1}]}}, "gitlens.views.lineHistory.setShowAvatarsOn": {"label": "Show Avatars", "menus": {"view/title": [{"when": "view == gitlens.views.lineHistory && !config.gitlens.views.lineHistory.avatars", "group": "5_gitlens", "order": 1}]}}, "gitlens.views.loadAllChildren": {"label": "Load All", "icon": "$(gitlens-expand)", "menus": {"view/item/context": [{"when": "viewItem =~ /gitlens:pager\\b/", "group": "inline", "order": 1}, {"when": "viewItem =~ /gitlens:pager\\b/ && !listMultiSelection", "group": "1_gitlens_actions", "order": 1}]}}, "gitlens.views.loadMoreChildren": {"label": "Load More"}, "gitlens.views.mergeBranchInto": {"label": "Merge Branch into Current Branch...", "enablement": "!operationInProgress", "menus": {"view/item/context": [{"when": "viewItem =~ /gitlens:branch\\b(?!.*?\\b\\+current\\b)(?!.*?\\b\\+closed\\b)/ && !listMultiSelection && !gitlens:readonly && !gitlens:untrusted && !gitlens:hasVirtualFolders", "group": "1_gitlens_actions_1", "order": 1}]}}, "gitlens.views.mergeChangesWithWorking": {"label": "Merge Changes (Manually)...", "menus": {"view/item/context": [{"when": "viewItem =~ /gitlens:file\\b(?=.*?\\b\\+stashed\\b)/ && !listMultiSelection", "group": "1_gitlens_actions", "order": 2}, {"when": "viewItem =~ /gitlens:file\\b((?=.*?\\b\\+committed\\b)|:results\\b)/ && !listMultiSelection", "group": "8_gitlens_actions", "order": 2}]}}, "gitlens.views.openBranchOnRemote": {"label": "Open Branch on Remote", "icon": "$(globe)", "menus": {"view/item/context": [{"when": "viewItem =~ /gitlens:branch\\b(?=.*?\\b\\+(tracking|remote)\\b)/ && !listMultiSelection", "group": "2_gitlens_quickopen", "order": 1}, {"when": "viewItem =~ /gitlens:status:upstream:(?!(missing|none))/ && !listMultiSelection && gitlens:repos:withRemotes", "group": "2_gitlens_quickopen_remote", "order": 1}, {"when": "viewItem =~ /gitlens:status:upstream:same/ && !gitlens:readonly && !gitlens:untrusted && !gitlens:hasVirtualFolders && gitlens:repos:withRemotes", "group": "inline", "order": 97, "alt": "gitlens.copyRemoteBranchUrl"}]}}, "gitlens.views.openBranchOnRemote.multi": {"label": "Open Branches on Remote", "icon": "$(globe)", "menus": {"view/item/context": [{"when": "viewItem =~ /gitlens:branch\\b(?=.*?\\b\\+(tracking|remote)\\b)/ && listMultiSelection", "group": "2_gitlens_quickopen", "order": 1}]}}, "gitlens.views.openChangedFileDiffs": {"label": "Open All Changes", "icon": "$(diff-multiple)", "menus": {"gitlens/commit/changes": [{"group": "1_gitlens", "order": 1}], "view/item/context": [{"when": "viewItem =~ /gitlens:(compare:results(?!:)\\b(?!.*?\\b\\+filtered\\b)|commit|stash|results:files|status-branch:files|status:upstream:(ahead|behind))\\b/ && !listMultiSelection && config.gitlens.views.openChangesInMultiDiffEditor", "group": "inline", "order": 90, "alt": "gitlens.views.openChangedFileDiffsWithWorking"}]}}, "gitlens.views.openChangedFileDiffsIndividually": {"label": "Open All Changes, Individually", "menus": {"gitlens/commit/changes": [{"when": "config.gitlens.views.openChangesInMultiDiffEditor", "group": "1_gitlens", "order": 2}]}}, "gitlens.views.openChangedFileDiffsWithMergeBase": {"label": "Open All Changes with Common Base", "icon": "$(diff-multiple)", "menus": {"view/item/context": [{"when": "viewItem =~ /gitlens:branch\\b(?!.*?\\b\\+current\\b)/ && !listMultiSelection", "group": "3_gitlens_explore", "order": 11}]}}, "gitlens.views.openChangedFileDiffsWithWorking": {"label": "Open All Changes with <PERSON> Tree", "icon": "$(diff-multiple)", "menus": {"gitlens/commit/changes": [{"group": "1_gitlens", "order": 3}]}}, "gitlens.views.openChangedFileDiffsWithWorkingIndividually": {"label": "Open All Changes with Working Tree, Individually", "menus": {"gitlens/commit/changes": [{"when": "config.gitlens.views.openChangesInMultiDiffEditor", "group": "1_gitlens", "order": 4}]}}, "gitlens.views.openChangedFileRevisions": {"label": "Open Files at Revision", "menus": {"gitlens/commit/changes": [{"group": "2_git<PERSON>s", "order": 2}]}}, "gitlens.views.openChangedFiles": {"label": "Open Files", "menus": {"gitlens/commit/changes": [{"group": "2_git<PERSON>s", "order": 1}]}}, "gitlens.views.openChanges": {"label": "Open Changes", "icon": "$(compare-changes)", "menus": {"view/item/context": [{"when": "viewItem =~ /gitlens:file\\b(?=.*?\\b\\+conflicted\\b)/ && view =~ /gitlens\\.views\\.(?!fileHistory|lineHistory)\\b/", "group": "inline", "order": 96}, {"when": "viewItem =~ /gitlens:file\\b/ && !listMultiSelection", "group": "2_gitlens_quickopen", "order": 1}]}}, "gitlens.views.openChangesWithMergeBase": {"label": "Open Changes with Common Base", "menus": {"gitlens/commit/file/changes": [{"when": "viewItem =~ /gitlens:file:results\\b/", "group": "1_gitlens", "order": 2}]}}, "gitlens.views.openChangesWithWorking": {"label": "Open Changes with Working File", "icon": "$(gitlens-compare-ref-working)", "menus": {"gitlens/commit/file/changes": [{"when": "viewItem =~ /gitlens:file\\b(?!.*?\\b\\+conflicted\\b)/", "group": "1_gitlens", "order": 1}], "view/item/context": [{"when": "viewItem =~ /gitlens:file\\b(?!.*?\\b\\+(conflicted|unstaged)\\b)/", "group": "inline", "order": 96}]}}, "gitlens.views.openCommitOnRemote": {"label": "Open Commit on Remote", "icon": "$(globe)", "menus": {"gitlens/commit/file/commit": [{"when": "viewItem =~ /gitlens:file\\b(?=.*?\\b\\+committed\\b)/ && view =~ /^gitlens\\.views\\.(fileHistory|lineHistory)/ && gitlens:repos:withRemotes", "group": "navigation", "order": 3, "alt": "gitlens.copyRemoteCommitUrl"}], "view/item/context": [{"when": "viewItem =~ /gitlens:commit\\b/ && gitlens:repos:withRemotes", "group": "inline", "order": 99, "alt": "gitlens.views.copyRemoteCommitUrl"}, {"when": "viewItem =~ /gitlens:commit\\b/ && !listMultiSelection && gitlens:repos:withRemotes", "group": "3_gitlens_explore", "order": 2}]}}, "gitlens.views.openCommitOnRemote.multi": {"label": "Open Commits on Remote", "icon": "$(globe)", "menus": {"view/item/context": [{"when": "viewItem =~ /gitlens:commit\\b/ && listMultiSelection && gitlens:repos:withRemotes", "group": "3_gitlens_explore", "order": 2}]}}, "gitlens.views.openDirectoryDiff": {"label": "Open Directory Comparison", "menus": {"view/item/context": [{"when": "viewItem =~ /gitlens:compare:results(?!:)\\b/ && !listMultiSelection", "group": "2_gitlens_quickopen", "order": 2}]}}, "gitlens.views.openDirectoryDiffWithWorking": {"label": "Directory Compare Working Tree to Here", "menus": {"view/item/context": [{"when": "viewItem =~ /gitlens:(branch|tag)\\b/ && !listMultiSelection && !gitlens:hasVirtualFolders", "group": "3_gitlens_explore", "order": 12}]}}, "gitlens.views.openFile": {"label": "Open File", "icon": "$(go-to-file)", "menus": {"view/item/context": [{"when": "viewItem =~ /gitlens:file\\b(?=.*?\\b\\+conflicted\\b)/ && view =~ /gitlens\\.views\\.(fileHistory|lineHistory)\\b/", "group": "inline", "order": 96}, {"when": "viewItem =~ /gitlens:(file|history:(file|line)|status:file)\\b/ && !listMultiSelection", "group": "2_gitlens_quickopen_file", "order": 3}, {"when": "viewItem =~ /gitlens:(history:(file|line)|status:file)\\b/", "group": "inline", "order": 1}]}}, "gitlens.views.openFileRevision": {"label": "Open File at Revision", "icon": "$(gitlens-open-revision)", "menus": {"view/item/context": [{"when": "viewItem =~ /gitlens:file(:results|\\b(?=.*?\\b\\+(committed|stashed)\\b))/", "group": "inline", "order": 1, "alt": "gitlens.views.openFile"}, {"when": "viewItem =~ /gitlens:file\\b((?=.*?\\b\\+(committed|stashed)\\b)|:results)/ && !listMultiSelection", "group": "2_gitlens_quickopen_file", "order": 4}]}}, "gitlens.views.openInIntegratedTerminal": {"label": "Open in Integrated Terminal", "menus": {"view/item/context": [{"when": "viewItem =~ /gitlens:repo-folder\\b/ && !listMultiSelection && !gitlens:hasVirtualFolders", "group": "2_gitlens_quickopen", "order": 2}, {"when": "viewItem =~ /gitlens:repository\\b/ && !listMultiSelection && !gitlens:hasVirtualFolders", "group": "2_gitlens_quickopen", "order": 2}, {"when": "viewItem =~ /gitlens:status:upstream\\b/ && !listMultiSelection && !gitlens:hasVirtualFolders", "group": "2_gitlens_quickopen_terminal", "order": 2}]}}, "gitlens.views.openInTerminal": {"label": "Open in Terminal", "menus": {"view/item/context": [{"when": "viewItem =~ /gitlens:repo-folder\\b/ && !listMultiSelection && !gitlens:hasVirtualFolders", "group": "2_gitlens_quickopen", "order": 1}, {"when": "viewItem =~ /gitlens:repository\\b/ && !listMultiSelection && !gitlens:hasVirtualFolders", "group": "2_gitlens_quickopen", "order": 1}, {"when": "viewItem =~ /gitlens:status:upstream\\b/ && !listMultiSelection && !gitlens:hasVirtualFolders", "group": "2_gitlens_quickopen_terminal", "order": 1}]}}, "gitlens.views.openInWorktree": {"label": "Open in Worktree", "icon": "$(empty-window)", "menus": {"view/item/context": [{"when": "viewItem =~ /gitlens:branch\\b(?!.*?\\b\\+(current|closed|checkedout|worktree)\\b)/ && !listMultiSelection && !gitlens:hasVirtualFolders", "group": "1_gitlens_action", "order": 3}, {"when": "viewItem =~ /gitlens:(pullrequest\\b|launchpad:item\\b(?=.*?\\b\\+pr\\b))/ && !listMultiSelection", "group": "inline", "order": 99}, {"when": "viewItem =~ /gitlens:(pullrequest\\b|launchpad:item\\b(?=.*?\\b\\+pr\\b))/ && !listMultiSelection", "group": "1_gitlens_actions", "order": 2}]}}, "gitlens.views.openOnlyChangedFiles": {"label": "Open Changed & Close Unchanged Files", "menus": {"gitlens/commit/changes": [{"group": "2_git<PERSON>s", "order": 3}]}}, "gitlens.views.openPausedOperationInRebaseEditor": {"label": "Open in Rebase Editor", "icon": "$(edit)", "menus": {"view/item/context": [{"when": "viewItem =~ /gitlens:paused-operation:rebase\\b/ && !listMultiSelection", "group": "inline", "order": 4}, {"when": "viewItem =~ /gitlens:paused-operation:rebase\\b/ && !listMultiSelection", "group": "2_git<PERSON>s", "order": 1}]}}, "gitlens.views.openPreviousChangesWithWorking": {"label": "Open Previous Changes with Working File", "menus": {"gitlens/commit/file/changes": [{"when": "viewItem =~ /gitlens:file\\b(?!.*?\\b\\+(conflicted|stashed|staged|unstaged)\\b)/", "group": "1_gitlens", "order": 2}]}}, "gitlens.views.openPullRequest": {"label": "Open Pull Request", "icon": "$(git-pull-request)", "menus": {"view/item/context": [{"when": "viewItem =~ /gitlens:pullrequest\\b/ && gitlens:action:openPullRequest > 1", "group": "inline", "order": 1}, {"when": "viewItem =~ /gitlens:pullrequest\\b/ && !listMultiSelection && gitlens:action:openPullRequest > 1", "group": "1_gitlens_actions", "order": 1}]}}, "gitlens.views.openPullRequestChanges": {"label": "Open Pull Request Changes", "icon": "$(diff-multiple)", "menus": {"view/item/context": [{"when": "viewItem =~ /gitlens:(pullrequest\\b(?=.*?\\b\\+refs\\b)|launchpad:item\\b(?=.*?\\b\\+pr\\b))/", "group": "inline", "order": 2}, {"when": "viewItem =~ /gitlens:(pullrequest\\b(?=.*?\\b\\+refs\\b)|launchpad:item\\b(?=.*?\\b\\+pr\\b))/ && !listMultiSelection", "group": "2_gitlens_quickopen", "order": 1}]}}, "gitlens.views.openPullRequestComparison": {"label": "Compare Pull Request", "icon": "$(compare-changes)", "menus": {"view/item/context": [{"when": "viewItem =~ /gitlens:(pullrequest\\b(?=.*?\\b\\+refs\\b)|launchpad:item\\b(?=.*?\\b\\+pr\\b))/ && !listMultiSelection", "group": "4_gitlens_compare", "order": 1}]}}, "gitlens.views.openUrl": {"label": "Open URL", "icon": "$(globe)", "menus": {"view/item/context": [{"when": "viewItem =~ /gitlens:autolinked:item\\b/", "group": "inline", "order": 99, "alt": "gitlens.views.copyUrl"}, {"when": "viewItem =~ /gitlens:autolinked:item\\b/ && !listMultiSelection", "group": "1_gitlens_actions", "order": 99}]}}, "gitlens.views.openUrl.multi": {"label": "Open URLs", "icon": "$(globe)", "menus": {"view/item/context": [{"when": "viewItem =~ /gitlens:autolinked:item\\b/ && listMultiSelection", "group": "1_gitlens_actions", "order": 99}]}}, "gitlens.views.openWorktree": {"label": "Open Worktree", "icon": "$(window)", "menus": {"view/item/context": [{"when": "viewItem =~ /gitlens:branch\\b(?=.*?\\b\\+worktree\\b)(?!.*?\\b\\+(current|closed|opened)\\b)/ && !gitlens:hasVirtualFolders", "group": "inline", "order": 7, "alt": "gitlens.views.openWorktreeInNewWindow"}, {"when": "viewItem =~ /gitlens:branch\\b(?=.*?\\b\\+worktree\\b)(?!.*?\\b\\+(current|closed|opened)\\b)/ && !listMultiSelection && !gitlens:hasVirtualFolders", "group": "1_gitlens_action", "order": 1}, {"when": "viewItem =~ /gitlens:worktree\\b(?!.*?\\b\\+active\\b)/", "group": "inline", "order": 1, "alt": "gitlens.views.openWorktreeInNewWindow"}, {"when": "viewItem =~ /gitlens:worktree\\b(?=.*?\\b\\+active\\b)/ && workspaceFolderCount != 1", "group": "inline", "order": 1, "alt": "gitlens.views.openWorktreeInNewWindow"}, {"when": "viewItem =~ /gitlens:worktree\\b(?!.*?\\b\\+active\\b)/ && !listMultiSelection", "group": "2_gitlens_quickopen", "order": 1}, {"when": "viewItem =~ /gitlens:worktree\\b(?=.*?\\b\\+active\\b)/ && !listMultiSelection && workspaceFolderCount != 1", "group": "2_gitlens_quickopen", "order": 1}]}}, "gitlens.views.openWorktreeInNewWindow": {"label": "Open Worktree in New Window", "icon": "$(empty-window)", "menus": {"view/item/context": [{"when": "viewItem =~ /gitlens:branch\\b(?=.*?\\b\\+worktree\\b)(?!.*?\\b\\+(current|closed|opened)\\b)/ && !listMultiSelection && !gitlens:hasVirtualFolders", "group": "1_gitlens_action", "order": 2}, {"when": "viewItem =~ /gitlens:worktree\\b(?!.*?\\b\\+active\\b)/ && !listMultiSelection", "group": "2_gitlens_quickopen", "order": 2}, {"when": "viewItem =~ /gitlens:worktree\\b(?=.*?\\b\\+active\\b)/ && !listMultiSelection && workspaceFolderCount != 1", "group": "2_gitlens_quickopen", "order": 2}]}}, "gitlens.views.openWorktreeInNewWindow.multi": {"label": "Open Worktrees in New Window", "icon": "$(empty-window)", "menus": {"view/item/context": [{"when": "viewItem =~ /gitlens:worktree\\b/ && listMultiSelection", "group": "2_gitlens_quickopen", "order": 2}]}}, "gitlens.views.patchDetails.close": {"label": "Close Patch", "icon": "$(close)", "menus": {"view/title": [{"when": "view == gitlens.views.patchDetails", "group": "navigation", "order": 99}]}}, "gitlens.views.patchDetails.refresh": {"label": "Refresh", "icon": "$(refresh)", "menus": {"view/title": [{"when": "view == gitlens.views.patchDetails", "group": "navigation", "order": 98}]}}, "gitlens.views.pruneRemote": {"label": "<PERSON><PERSON><PERSON>", "enablement": "!operationInProgress", "menus": {"view/item/context": [{"when": "viewItem =~ /gitlens:remote\\b/ && !listMultiSelection && !gitlens:readonly && !gitlens:untrusted && !gitlens:hasVirtualFolders", "group": "1_gitlens_actions", "order": 2}]}}, "gitlens.views.publishBranch": {"label": "Publish Branch", "icon": "$(cloud-upload)", "enablement": "!operationInProgress", "menus": {"view/item/context": [{"when": "viewItem =~ /gitlens:branch\\b(?!.*?\\b\\+(closed|remote|tracking)\\b)/ && !gitlens:readonly && !gitlens:untrusted && !gitlens:hasVirtualFolders && gitlens:repos:withRemotes", "group": "inline", "order": 8}, {"when": "viewItem =~ /gitlens:branch\\b(?!.*?\\b\\+(closed|remote|tracking)\\b)/ && !listMultiSelection && !gitlens:readonly && !gitlens:untrusted && !gitlens:hasVirtualFolders && gitlens:repos:withRemotes", "group": "1_gitlens_actions", "order": 2}, {"when": "viewItem =~ /gitlens:status(\\-branch)?:upstream:(missing|none)/ && !gitlens:readonly && !gitlens:untrusted && !gitlens:hasVirtualFolders && gitlens:repos:withRemotes", "group": "inline", "order": 99}]}}, "gitlens.views.publishRepository": {"label": "Publish Repository", "icon": "$(cloud-upload)", "enablement": "!operationInProgress", "menus": {"view/item/context": [{"when": "viewItem =~ /gitlens:status(\\-branch)?:upstream:(missing|none)/ && !gitlens:readonly && !gitlens:untrusted && !gitlens:hasVirtualFolders && !gitlens:repos:withRemotes", "group": "inline", "order": 99}]}}, "gitlens.views.pull": {"label": "<PERSON><PERSON>", "icon": "$(repo-pull)", "enablement": "!operationInProgress", "menus": {"view/item/context": [{"when": "viewItem =~ /gitlens:branch\\b(?=.*?\\b\\+behind\\b)(?!.*?\\b\\+closed\\b)/ && !gitlens:readonly && !gitlens:untrusted && !gitlens:hasVirtualFolders && gitlens:repos:withRemotes", "group": "inline", "order": 8, "alt": "gitlens.views.fetch"}, {"when": "viewItem =~ /gitlens:branch\\b(?=.*?\\b\\+(behind|tracking)\\b)(?!.*?\\b\\+closed\\b)/ && !listMultiSelection && !gitlens:readonly && !gitlens:untrusted && !gitlens:hasVirtualFolders && gitlens:repos:withRemotes", "group": "1_gitlens_actions", "order": 2}, {"when": "viewItem =~ /gitlens:repo-folder\\b(?=.*?\\b\\+behind\\b)/ && !gitlens:readonly && !gitlens:untrusted && !gitlens:hasVirtualFolders && gitlens:repos:withRemotes", "group": "inline", "order": 97}, {"when": "viewItem =~ /gitlens:repo-folder\\b/ && !listMultiSelection && !gitlens:readonly && !gitlens:untrusted && !gitlens:hasVirtualFolders && gitlens:repos:withRemotes", "group": "1_gitlens_actions", "order": 1}, {"when": "viewItem =~ /gitlens:repository\\b(?!.*?\\b\\+closed\\b)/ && !gitlens:readonly && !gitlens:untrusted && !gitlens:hasVirtualFolders && gitlens:repos:withRemotes", "group": "inline", "order": 97}, {"when": "viewItem =~ /gitlens:repository\\b(?!.*?\\b\\+closed\\b)/ && !listMultiSelection && !gitlens:readonly && !gitlens:untrusted && !gitlens:hasVirtualFolders && gitlens:repos:withRemotes", "group": "1_gitlens_actions", "order": 1}, {"when": "viewItem =~ /gitlens:status(\\-branch)?:upstream:behind/ && !gitlens:readonly && !gitlens:untrusted && !gitlens:hasVirtualFolders && gitlens:repos:withRemotes", "group": "inline", "order": 99}, {"when": "viewItem == gitlens:status:upstream:behind && !listMultiSelection && !gitlens:readonly && !gitlens:untrusted && !gitlens:hasVirtualFolders && gitlens:repos:withRemotes", "group": "1_gitlens_actions", "order": 1}]}}, "gitlens.views.pullRequest.close": {"label": "Close", "icon": "$(close)", "menus": {"view/title": [{"when": "view == gitlens.views.pullRequest", "group": "navigation", "order": 99}]}}, "gitlens.views.pullRequest.copy": {"label": "Copy", "keybindings": [{"key": "ctrl+c", "when": "focusedView == gitlens.views.pullRequest", "mac": "cmd+c"}]}, "gitlens.views.pullRequest.refresh": {"label": "Refresh", "icon": "$(refresh)", "menus": {"view/title": [{"when": "view == gitlens.views.pullRequest", "group": "navigation", "order": 98}]}}, "gitlens.views.pullRequest.setFilesLayoutToAuto": {"label": "View Files as Auto", "icon": "$(list-tree)", "enablement": "config.gitlens.views.pullRequest.files.layout != auto", "menus": {"view/title": [{"when": "view == gitlens.views.pullRequest && config.gitlens.views.pullRequest.files.layout == tree", "group": "navigation", "order": 50}]}}, "gitlens.views.pullRequest.setFilesLayoutToList": {"label": "View Files as List", "icon": "$(gitlens-list-auto)", "enablement": "config.gitlens.views.pullRequest.files.layout != list", "menus": {"view/title": [{"when": "view == gitlens.views.pullRequest && config.gitlens.views.pullRequest.files.layout == auto", "group": "navigation", "order": 50}]}}, "gitlens.views.pullRequest.setFilesLayoutToTree": {"label": "View Files as Tree", "icon": "$(list-flat)", "enablement": "config.gitlens.views.pullRequest.files.layout != tree", "menus": {"view/title": [{"when": "view == gitlens.views.pullRequest && config.gitlens.views.pullRequest.files.layout == list", "group": "navigation", "order": 50}]}}, "gitlens.views.pullRequest.setShowAvatarsOff": {"label": "Hide Avatars", "menus": {"view/title": [{"when": "view == gitlens.views.pullRequest && config.gitlens.views.pullRequest.avatars", "group": "5_gitlens", "order": 1}]}}, "gitlens.views.pullRequest.setShowAvatarsOn": {"label": "Show Avatars", "menus": {"view/title": [{"when": "view == gitlens.views.pullRequest && !config.gitlens.views.pullRequest.avatars", "group": "5_gitlens", "order": 1}]}}, "gitlens.views.push": {"label": "<PERSON><PERSON>", "icon": "$(repo-push)", "enablement": "!operationInProgress", "menus": {"gitlens/commit/file/commit": [{"when": "viewItem =~ /gitlens:file\\b(?=.*?\\b\\+committed\\b)(?=.*?\\b\\+current\\b)(?=.*?\\b\\+unpublished\\b)(?=.*?\\b\\+HEAD\\b)/ && !gitlens:readonly && !gitlens:untrusted && !gitlens:hasVirtualFolders && gitlens:repos:withRemotes", "group": "1_gitlens_actions", "order": 1}], "view/item/context": [{"when": "viewItem =~ /gitlens:branch\\b(?=.*?\\b\\+ahead\\b)(?!.*?\\b\\+(behind|closed)\\b)/ && !gitlens:readonly && !gitlens:untrusted && !gitlens:hasVirtualFolders && gitlens:repos:withRemotes", "group": "inline", "order": 8}, {"when": "viewItem =~ /gitlens:branch\\b(?=.*?\\b\\+ahead\\b)(?!.*?\\b\\+(behind|closed)\\b)/ && !listMultiSelection && !gitlens:readonly && !gitlens:untrusted && !gitlens:hasVirtualFolders && gitlens:repos:withRemotes", "group": "1_gitlens_actions", "order": 2}, {"when": "viewItem =~ /gitlens:commit\\b(?=.*?\\b\\+current\\b)(?=.*?\\b\\+unpublished\\b)(?=.*?\\b\\+HEAD\\b)/ && !gitlens:readonly && !gitlens:untrusted && !gitlens:hasVirtualFolders && gitlens:repos:withRemotes", "group": "inline", "order": 79, "alt": "gitlens.views.pushWithForce"}, {"when": "viewItem =~ /gitlens:commit\\b(?=.*?\\b\\+current\\b)(?=.*?\\b\\+unpublished\\b)(?=.*?\\b\\+HEAD\\b)/ && !listMultiSelection && !gitlens:readonly && !gitlens:untrusted && !gitlens:hasVirtualFolders && gitlens:repos:withRemotes", "group": "1_gitlens_actions", "order": 2}, {"when": "viewItem =~ /gitlens:file\\b(?=.*?\\b\\+committed\\b)(?=.*?\\b\\+current\\b)(?=.*?\\b\\+unpublished\\b)(?=.*?\\b\\+HEAD\\b)/ && !gitlens:readonly && !gitlens:untrusted && !gitlens:hasVirtualFolders && gitlens:repos:withRemotes", "group": "inline", "order": -1}, {"when": "viewItem =~ /gitlens:repo-folder\\b(?=.*?\\b\\+ahead\\b)(?!.*?\\b\\+behind\\b)/ && !gitlens:readonly && !gitlens:untrusted && !gitlens:hasVirtualFolders && gitlens:repos:withRemotes", "group": "inline", "order": 96, "alt": "gitlens.views.pushWithForce"}, {"when": "viewItem =~ /gitlens:repo-folder\\b/ && !listMultiSelection && !gitlens:readonly && !gitlens:untrusted && !gitlens:hasVirtualFolders && gitlens:repos:withRemotes", "group": "1_gitlens_actions", "order": 1}, {"when": "viewItem =~ /gitlens:repository\\b(?!.*?\\b\\+closed\\b)/ && !gitlens:readonly && !gitlens:untrusted && !gitlens:hasVirtualFolders && gitlens:repos:withRemotes", "group": "inline", "order": 96, "alt": "gitlens.views.pushWithForce"}, {"when": "viewItem =~ /gitlens:repository\\b(?!.*?\\b\\+closed\\b)/ && !listMultiSelection && !gitlens:readonly && !gitlens:untrusted && !gitlens:hasVirtualFolders && gitlens:repos:withRemotes", "group": "1_gitlens_actions", "order": 1}, {"when": "viewItem =~ /gitlens:status(\\-branch)?:upstream:ahead/ && !gitlens:readonly && !gitlens:untrusted && !gitlens:hasVirtualFolders && gitlens:repos:withRemotes", "group": "inline", "order": 99, "alt": "gitlens.views.pushWithForce"}, {"when": "viewItem == gitlens:status:upstream:ahead && !listMultiSelection && !gitlens:readonly && !gitlens:untrusted && !gitlens:hasVirtualFolders && gitlens:repos:withRemotes", "group": "1_gitlens_actions", "order": 1}]}}, "gitlens.views.pushToCommit": {"label": "Push to Commit...", "icon": "$(repo-push)", "enablement": "!operationInProgress", "menus": {"gitlens/commit/file/commit": [{"when": "viewItem =~ /gitlens:file\\b(?=.*?\\b\\+committed\\b)(?=.*?\\b\\+current\\b)(?=.*?\\b\\+unpublished\\b)(?!.*?\\b\\+HEAD\\b)/ && !gitlens:readonly && !gitlens:untrusted && !gitlens:hasVirtualFolders && gitlens:repos:withRemotes", "group": "1_gitlens_actions", "order": 2}], "view/item/context": [{"when": "viewItem =~ /gitlens:commit\\b(?=.*?\\b\\+current\\b)(?=.*?\\b\\+unpublished\\b)(?!.*?\\b\\+HEAD\\b)/ && !gitlens:readonly && !gitlens:untrusted && !gitlens:hasVirtualFolders && gitlens:repos:withRemotes", "group": "inline", "order": 80}, {"when": "viewItem =~ /gitlens:commit\\b(?=.*?\\b\\+current\\b)(?=.*?\\b\\+unpublished\\b)(?!.*?\\b\\+HEAD\\b)/ && !listMultiSelection && !gitlens:readonly && !gitlens:untrusted && !gitlens:hasVirtualFolders && gitlens:repos:withRemotes", "group": "1_gitlens_actions", "order": 2}, {"when": "viewItem =~ /gitlens:file\\b(?=.*?\\b\\+committed\\b)(?=.*?\\b\\+current\\b)(?=.*?\\b\\+unpublished\\b)(?!.*?\\b\\+HEAD\\b)/ && !gitlens:readonly && !gitlens:untrusted && !gitlens:hasVirtualFolders && gitlens:repos:withRemotes", "group": "inline", "order": -1}]}}, "gitlens.views.pushWithForce": {"label": "Push (force)", "icon": "$(repo-force-push)", "enablement": "!operationInProgress", "menus": {"view/item/context": [{"when": "viewItem =~ /gitlens:repo-folder\\b/ && !listMultiSelection && !gitlens:readonly && !gitlens:untrusted && !gitlens:hasVirtualFolders && gitlens:repos:withRemotes", "group": "1_gitlens_actions", "order": 2}, {"when": "viewItem =~ /gitlens:repository\\b(?!.*?\\b\\+closed\\b)/ && !listMultiSelection && !gitlens:readonly && !gitlens:untrusted && !gitlens:hasVirtualFolders && gitlens:repos:withRemotes", "group": "1_gitlens_actions", "order": 2}, {"when": "viewItem == gitlens:status:upstream:ahead && !listMultiSelection && !gitlens:readonly && !gitlens:untrusted && !gitlens:hasVirtualFolders && gitlens:repos:withRemotes", "group": "1_gitlens_actions", "order": 2}]}}, "gitlens.views.rebaseOntoBranch": {"label": "Rebase Current Branch onto Branch...", "enablement": "!operationInProgress", "menus": {"view/item/context": [{"when": "viewItem =~ /gitlens:branch\\b(?!.*?\\b\\+current\\b)(?!.*?\\b\\+closed\\b)/ && !listMultiSelection && !gitlens:readonly && !gitlens:untrusted && !gitlens:hasVirtualFolders", "group": "1_gitlens_actions_1", "order": 2}]}}, "gitlens.views.rebaseOntoCommit": {"label": "Rebase Current Branch onto Commit...", "enablement": "!operationInProgress", "menus": {"gitlens/commit/file/commit": [{"when": "viewItem =~ /gitlens:file\\b(?=.*?\\b\\+committed\\b)(?=.*?\\b\\+current\\b)/ && !gitlens:readonly && !gitlens:untrusted && !gitlens:hasVirtualFolders", "group": "1_gitlens_actions", "order": 6}], "view/item/context": [{"when": "viewItem =~ /gitlens:commit\\b(?!.*?\\b\\+rebase\\b)/ && !listMultiSelection && !gitlens:readonly && !gitlens:untrusted && !gitlens:hasVirtualFolders", "group": "1_gitlens_actions", "order": 6}]}}, "gitlens.views.rebaseOntoUpstream": {"label": "Rebase Current Branch onto Upstream...", "enablement": "!operationInProgress", "menus": {"view/item/context": [{"when": "viewItem =~ /gitlens:branch\\b(?=.*?\\b\\+current\\b)(?=.*?\\b\\+tracking\\b)(?!.*?\\b\\+closed\\b)/ && !listMultiSelection && !gitlens:readonly && !gitlens:untrusted && !gitlens:hasVirtualFolders", "group": "1_gitlens_actions_1", "order": 2}]}}, "gitlens.views.refreshNode": {"label": "Refresh", "icon": "$(refresh)", "menus": {"view/item/context": [{"when": "viewItem =~ /gitlens:(?!(file|message|date-marker)\\b)/", "group": "9_gitlens_z", "order": 99}, {"when": "viewItem =~ /gitlens:compare:(branch(?=.*?\\b\\+comparing\\b)|results(?!:))\\b/", "group": "inline", "order": 97}, {"when": "viewItem =~ /gitlens:search:results(?!:)\\b/", "group": "inline", "order": 97}]}}, "gitlens.views.remotes.attach": {"label": "Group into GitLens View", "icon": "$(close)", "menus": {"view/title": [{"when": "view == gitlens.views.remotes", "group": "navigation", "order": 99}, {"when": "view == gitlens.views.remotes", "group": "9_git<PERSON>s", "order": 0}]}}, "gitlens.views.remotes.copy": {"label": "Copy", "keybindings": [{"key": "ctrl+c", "when": "focusedView == gitlens.views.remotes", "mac": "cmd+c"}]}, "gitlens.views.remotes.refresh": {"label": "Refresh", "icon": "$(refresh)", "menus": {"view/title": [{"when": "view == gitlens.views.remotes", "group": "navigation", "order": 98}]}}, "gitlens.views.remotes.setFilesLayoutToAuto": {"label": "View Files as Auto", "icon": "$(list-tree)", "enablement": "config.gitlens.views.remotes.files.layout != auto", "menus": {"gitlens/views/grouped/more": [{"when": "gitlens:views:scm:grouped:view == remotes && view == gitlens.views.scm.grouped", "group": "3_git<PERSON><PERSON>_", "order": 1}], "view/title": [{"when": "view == gitlens.views.remotes", "group": "3_git<PERSON><PERSON>_", "order": 0}, {"when": "view == gitlens.views.scm.grouped && gitlens:views:scm:grouped:view == remotes", "group": "3_git<PERSON><PERSON>_", "order": 1}]}}, "gitlens.views.remotes.setFilesLayoutToList": {"label": "View Files as List", "icon": "$(gitlens-list-auto)", "enablement": "config.gitlens.views.remotes.files.layout != list", "menus": {"gitlens/views/grouped/more": [{"when": "gitlens:views:scm:grouped:view == remotes && view == gitlens.views.scm.grouped", "group": "3_git<PERSON><PERSON>_", "order": 1}], "view/title": [{"when": "view == gitlens.views.remotes", "group": "3_git<PERSON><PERSON>_", "order": 0}, {"when": "view == gitlens.views.scm.grouped && gitlens:views:scm:grouped:view == remotes", "group": "3_git<PERSON><PERSON>_", "order": 1}]}}, "gitlens.views.remotes.setFilesLayoutToTree": {"label": "View Files as Tree", "icon": "$(list-flat)", "enablement": "config.gitlens.views.remotes.files.layout != tree", "menus": {"gitlens/views/grouped/more": [{"when": "gitlens:views:scm:grouped:view == remotes && view == gitlens.views.scm.grouped", "group": "3_git<PERSON><PERSON>_", "order": 1}], "view/title": [{"when": "view == gitlens.views.remotes", "group": "3_git<PERSON><PERSON>_", "order": 0}, {"when": "view == gitlens.views.scm.grouped && gitlens:views:scm:grouped:view == remotes", "group": "3_git<PERSON><PERSON>_", "order": 1}]}}, "gitlens.views.remotes.setLayoutToList": {"label": "View as List", "icon": "$(list-tree)", "menus": {"gitlens/views/grouped/more": [{"when": "gitlens:views:scm:grouped:view == remotes && view == gitlens.views.scm.grouped && config.gitlens.views.remotes.branches.layout == tree", "group": "3_git<PERSON>s", "order": 2}], "view/title": [{"when": "view == gitlens.views.remotes && config.gitlens.views.remotes.branches.layout == tree", "group": "navigation", "order": 50}, {"when": "view == gitlens.views.scm.grouped && gitlens:views:scm:grouped:view == remotes && config.gitlens.views.remotes.branches.layout == tree", "group": "3_git<PERSON>s", "order": 2}]}}, "gitlens.views.remotes.setLayoutToTree": {"label": "View as Tree", "icon": "$(list-flat)", "menus": {"gitlens/views/grouped/more": [{"when": "gitlens:views:scm:grouped:view == remotes && view == gitlens.views.scm.grouped && config.gitlens.views.remotes.branches.layout == list", "group": "3_git<PERSON>s", "order": 2}], "view/title": [{"when": "view == gitlens.views.remotes && config.gitlens.views.remotes.branches.layout == list", "group": "navigation", "order": 50}, {"when": "view == gitlens.views.scm.grouped && gitlens:views:scm:grouped:view == remotes && config.gitlens.views.remotes.branches.layout == list", "group": "3_git<PERSON>s", "order": 2}]}}, "gitlens.views.remotes.setShowAvatarsOff": {"label": "Hide Avatars", "menus": {"gitlens/views/grouped/more": [{"when": "gitlens:views:scm:grouped:view == remotes && view == gitlens.views.scm.grouped && config.gitlens.views.remotes.avatars", "group": "5_gitlens", "order": 1}], "view/title": [{"when": "view == gitlens.views.remotes && config.gitlens.views.remotes.avatars", "group": "5_gitlens", "order": 1}, {"when": "view == gitlens.views.scm.grouped && gitlens:views:scm:grouped:view == remotes && config.gitlens.views.remotes.avatars", "group": "5_gitlens", "order": 1}]}}, "gitlens.views.remotes.setShowAvatarsOn": {"label": "Show Avatars", "menus": {"gitlens/views/grouped/more": [{"when": "gitlens:views:scm:grouped:view == remotes && view == gitlens.views.scm.grouped && !config.gitlens.views.remotes.avatars", "group": "5_gitlens", "order": 1}], "view/title": [{"when": "view == gitlens.views.remotes && !config.gitlens.views.remotes.avatars", "group": "5_gitlens", "order": 1}, {"when": "view == gitlens.views.scm.grouped && gitlens:views:scm:grouped:view == remotes && !config.gitlens.views.remotes.avatars", "group": "5_gitlens", "order": 1}]}}, "gitlens.views.remotes.setShowBranchPullRequestOff": {"label": "Hide Branch Pull Requests", "menus": {"gitlens/views/grouped/more": [{"when": "gitlens:views:scm:grouped:view == remotes && view == gitlens.views.scm.grouped && config.gitlens.views.remotes.pullRequests.enabled && config.gitlens.views.remotes.pullRequests.showForBranches", "group": "5_gitlens", "order": 2}], "view/title": [{"when": "view == gitlens.views.remotes && config.gitlens.views.remotes.pullRequests.enabled && config.gitlens.views.remotes.pullRequests.showForBranches", "group": "5_gitlens", "order": 2}, {"when": "view == gitlens.views.scm.grouped && gitlens:views:scm:grouped:view == remotes && config.gitlens.views.remotes.pullRequests.enabled && config.gitlens.views.remotes.pullRequests.showForBranches", "group": "5_gitlens", "order": 2}]}}, "gitlens.views.remotes.setShowBranchPullRequestOn": {"label": "Show Branch Pull Requests", "menus": {"gitlens/views/grouped/more": [{"when": "gitlens:views:scm:grouped:view == remotes && view == gitlens.views.scm.grouped && !config.gitlens.views.remotes.pullRequests.enabled && !config.gitlens.views.remotes.pullRequests.showForBranches", "group": "5_gitlens", "order": 2}], "view/title": [{"when": "view == gitlens.views.remotes && !config.gitlens.views.remotes.pullRequests.enabled && !config.gitlens.views.remotes.pullRequests.showForBranches", "group": "5_gitlens", "order": 2}, {"when": "view == gitlens.views.scm.grouped && gitlens:views:scm:grouped:view == remotes && !config.gitlens.views.remotes.pullRequests.enabled && !config.gitlens.views.remotes.pullRequests.showForBranches", "group": "5_gitlens", "order": 2}]}}, "gitlens.views.remotes.viewOptionsTitle": {"label": "Remotes View Options", "enablement": "false", "menus": {"view/title": [{"when": "view == gitlens.views.scm.grouped && gitlens:views:scm:grouped:view == remotes", "group": "1_gitlens_1", "order": 1}]}}, "gitlens.views.removeRemote": {"label": "Remove Remote...", "enablement": "!operationInProgress", "menus": {"view/item/context": [{"when": "viewItem =~ /gitlens:remote\\b/ && !listMultiSelection && !gitlens:readonly && !gitlens:untrusted && !gitlens:hasVirtualFolders", "group": "6_gitlens_terminal", "order": 1}]}}, "gitlens.views.renameBranch": {"label": "Rename Branch...", "enablement": "!operationInProgress", "menus": {"view/item/context": [{"when": "viewItem =~ /gitlens:branch\\b(?!.*?\\b\\+closed\\b)/ && !listMultiSelection && !gitlens:readonly && !gitlens:untrusted && !gitlens:hasVirtualFolders", "group": "1_gitlens_actions_2", "order": 2}]}}, "gitlens.views.repositories.attach": {"label": "Group into GitLens View", "icon": "$(close)", "menus": {"view/title": [{"when": "view == gitlens.views.repositories", "group": "navigation", "order": 99}, {"when": "view == gitlens.views.repositories", "group": "9_git<PERSON>s", "order": 0}]}}, "gitlens.views.repositories.copy": {"label": "Copy", "keybindings": [{"key": "ctrl+c", "when": "focusedView == gitlens.views.repositories", "mac": "cmd+c"}]}, "gitlens.views.repositories.refresh": {"label": "Refresh", "icon": "$(refresh)", "menus": {"view/title": [{"when": "view == gitlens.views.repositories", "group": "navigation", "order": 98}]}}, "gitlens.views.repositories.setAutoRefreshToOff": {"label": "Disable Automatic Refresh", "menus": {"gitlens/views/grouped/more": [{"when": "gitlens:views:scm:grouped:view == repositories && view == gitlens.views.scm.grouped && gitlens:views:repositories:autoRefresh && config.gitlens.views.repositories.autoRefresh", "group": "2_git<PERSON>s", "order": 1}], "view/title": [{"when": "view == gitlens.views.repositories && gitlens:views:repositories:autoRefresh && config.gitlens.views.repositories.autoRefresh", "group": "2_git<PERSON>s", "order": 0}, {"when": "view == gitlens.views.scm.grouped && gitlens:views:scm:grouped:view == repositories && gitlens:views:repositories:autoRefresh && config.gitlens.views.repositories.autoRefresh", "group": "2_git<PERSON>s", "order": 1}]}}, "gitlens.views.repositories.setAutoRefreshToOn": {"label": "Enable Automatic Refresh", "menus": {"gitlens/views/grouped/more": [{"when": "gitlens:views:scm:grouped:view == repositories && view == gitlens.views.scm.grouped && !gitlens:views:repositories:autoRefresh && config.gitlens.views.repositories.autoRefresh", "group": "2_git<PERSON>s", "order": 1}], "view/title": [{"when": "view == gitlens.views.repositories && !gitlens:views:repositories:autoRefresh && config.gitlens.views.repositories.autoRefresh", "group": "2_git<PERSON>s", "order": 0}, {"when": "view == gitlens.views.scm.grouped && gitlens:views:scm:grouped:view == repositories && !gitlens:views:repositories:autoRefresh && config.gitlens.views.repositories.autoRefresh", "group": "2_git<PERSON>s", "order": 1}]}}, "gitlens.views.repositories.setBranchesLayoutToList": {"label": "View Branches as List", "icon": "$(list-tree)", "menus": {"view/item/context": [{"when": "viewItem =~ /gitlens:branches\\b/ && config.gitlens.views.repositories.branches.layout == tree", "group": "inline", "order": 50}, {"when": "viewItem =~ /gitlens:remotes\\b/ && config.gitlens.views.repositories.branches.layout == tree", "group": "inline", "order": 50}]}}, "gitlens.views.repositories.setBranchesLayoutToTree": {"label": "View Branches as Tree", "icon": "$(list-flat)", "menus": {"view/item/context": [{"when": "viewItem =~ /gitlens:branches\\b/ && config.gitlens.views.repositories.branches.layout == list", "group": "inline", "order": 50}, {"when": "viewItem =~ /gitlens:remotes\\b/ && config.gitlens.views.repositories.branches.layout == list", "group": "inline", "order": 50}]}}, "gitlens.views.repositories.setBranchesShowBranchComparisonOff": {"label": "Hide Branch Comparisons", "menus": {"view/item/context": [{"when": "viewItem =~ /gitlens:branches\\b/ && view == gitlens.views.repositories && !listMultiSelection && config.gitlens.views.repositories.branches.showBranchComparison", "group": "8_gitlens_toggles", "order": 1}, {"when": "viewItem =~ /gitlens:compare:branch(?!.*?\\b\\+root\\b)\\b/ && view == gitlens.views.repositories && !listMultiSelection", "group": "8_gitlens_toggles", "order": 1}]}}, "gitlens.views.repositories.setBranchesShowBranchComparisonOn": {"label": "Show Branch Comparisons", "menus": {"view/item/context": [{"when": "viewItem =~ /gitlens:branches\\b/ && view == gitlens.views.repositories && !listMultiSelection && !config.gitlens.views.repositories.branches.showBranchComparison", "group": "8_gitlens_toggles", "order": 1}]}}, "gitlens.views.repositories.setFilesLayoutToAuto": {"label": "View Files as Auto", "icon": "$(list-tree)", "menus": {"gitlens/views/grouped/more": [{"when": "gitlens:views:scm:grouped:view == repositories && view == gitlens.views.scm.grouped && config.gitlens.views.repositories.files.layout == tree", "group": "3_git<PERSON>s", "order": 1}], "view/title": [{"when": "view == gitlens.views.repositories && config.gitlens.views.repositories.files.layout == tree", "group": "3_git<PERSON>s", "order": 0}, {"when": "view == gitlens.views.scm.grouped && gitlens:views:scm:grouped:view == repositories && config.gitlens.views.repositories.files.layout == tree", "group": "3_git<PERSON>s", "order": 1}]}}, "gitlens.views.repositories.setFilesLayoutToList": {"label": "View Files as List", "icon": "$(gitlens-list-auto)", "menus": {"gitlens/views/grouped/more": [{"when": "gitlens:views:scm:grouped:view == repositories && view == gitlens.views.scm.grouped && config.gitlens.views.repositories.files.layout == auto", "group": "3_git<PERSON>s", "order": 1}], "view/title": [{"when": "view == gitlens.views.repositories && config.gitlens.views.repositories.files.layout == auto", "group": "3_git<PERSON>s", "order": 0}, {"when": "view == gitlens.views.scm.grouped && gitlens:views:scm:grouped:view == repositories && config.gitlens.views.repositories.files.layout == auto", "group": "3_git<PERSON>s", "order": 1}]}}, "gitlens.views.repositories.setFilesLayoutToTree": {"label": "View Files as Tree", "icon": "$(list-flat)", "menus": {"gitlens/views/grouped/more": [{"when": "gitlens:views:scm:grouped:view == repositories && view == gitlens.views.scm.grouped && config.gitlens.views.repositories.files.layout == list", "group": "3_git<PERSON>s", "order": 1}], "view/title": [{"when": "view == gitlens.views.repositories && config.gitlens.views.repositories.files.layout == list", "group": "3_git<PERSON>s", "order": 0}, {"when": "view == gitlens.views.scm.grouped && gitlens:views:scm:grouped:view == repositories && config.gitlens.views.repositories.files.layout == list", "group": "3_git<PERSON>s", "order": 1}]}}, "gitlens.views.repositories.setShowAvatarsOff": {"label": "Hide Avatars", "menus": {"gitlens/views/grouped/more": [{"when": "gitlens:views:scm:grouped:view == repositories && view == gitlens.views.scm.grouped && config.gitlens.views.repositories.avatars", "group": "5_gitlens", "order": 1}], "view/title": [{"when": "view == gitlens.views.repositories && config.gitlens.views.repositories.avatars", "group": "5_gitlens", "order": 1}, {"when": "view == gitlens.views.scm.grouped && gitlens:views:scm:grouped:view == repositories && config.gitlens.views.repositories.avatars", "group": "5_gitlens", "order": 1}]}}, "gitlens.views.repositories.setShowAvatarsOn": {"label": "Show Avatars", "menus": {"gitlens/views/grouped/more": [{"when": "gitlens:views:scm:grouped:view == repositories && view == gitlens.views.scm.grouped && !config.gitlens.views.repositories.avatars", "group": "5_gitlens", "order": 1}], "view/title": [{"when": "view == gitlens.views.repositories && !config.gitlens.views.repositories.avatars", "group": "5_gitlens", "order": 1}, {"when": "view == gitlens.views.scm.grouped && gitlens:views:scm:grouped:view == repositories && !config.gitlens.views.repositories.avatars", "group": "5_gitlens", "order": 1}]}}, "gitlens.views.repositories.setShowBranchComparisonOff": {"label": "Hide Branch Comparison", "menus": {"gitlens/view/repositories/sections": [{"when": "config.gitlens.views.repositories.showBranchComparison", "group": "1_gitlens", "order": 0}]}}, "gitlens.views.repositories.setShowBranchComparisonOn": {"label": "Show Branch Comparison", "menus": {"gitlens/view/repositories/sections": [{"when": "!config.gitlens.views.repositories.showBranchComparison", "group": "1_gitlens", "order": 0}]}}, "gitlens.views.repositories.setShowBranchesOff": {"label": "Hide Branches", "menus": {"gitlens/view/repositories/sections": [{"when": "config.gitlens.views.repositories.showBranches", "group": "2_git<PERSON>s", "order": 3}]}}, "gitlens.views.repositories.setShowBranchesOn": {"label": "Show Branches", "menus": {"gitlens/view/repositories/sections": [{"when": "!config.gitlens.views.repositories.showBranches", "group": "2_git<PERSON>s", "order": 3}]}}, "gitlens.views.repositories.setShowCommitsOff": {"label": "Hide Commits", "menus": {"gitlens/view/repositories/sections": [{"when": "config.gitlens.views.repositories.showCommits", "group": "2_git<PERSON>s", "order": 2}]}}, "gitlens.views.repositories.setShowCommitsOn": {"label": "Show Commits", "menus": {"gitlens/view/repositories/sections": [{"when": "!config.gitlens.views.repositories.showCommits", "group": "2_git<PERSON>s", "order": 2}]}}, "gitlens.views.repositories.setShowContributorsOff": {"label": "Hide Contributors", "menus": {"gitlens/view/repositories/sections": [{"when": "config.gitlens.views.repositories.showContributors", "group": "2_git<PERSON>s", "order": 8}]}}, "gitlens.views.repositories.setShowContributorsOn": {"label": "Show Contributors", "menus": {"gitlens/view/repositories/sections": [{"when": "!config.gitlens.views.repositories.showContributors", "group": "2_git<PERSON>s", "order": 8}]}}, "gitlens.views.repositories.setShowRemotesOff": {"label": "Hide Remotes", "menus": {"gitlens/view/repositories/sections": [{"when": "config.gitlens.views.repositories.showRemotes", "group": "2_git<PERSON>s", "order": 4}]}}, "gitlens.views.repositories.setShowRemotesOn": {"label": "Show Remotes", "menus": {"gitlens/view/repositories/sections": [{"when": "!config.gitlens.views.repositories.showRemotes", "group": "2_git<PERSON>s", "order": 4}]}}, "gitlens.views.repositories.setShowSectionOff": {"label": "<PERSON>de", "menus": {"view/item/context": [{"when": "viewItem =~ /gitlens:(compare:branch(?=.*?\\b\\+root\\b)|branches|branch(?=.*?\\b\\+commits\\b)|reflog|remotes|stashes|status:upstream|tags)\\b/ && view == gitlens.views.repositories && !listMultiSelection", "group": "8_gitlens_toggles", "order": 99}]}}, "gitlens.views.repositories.setShowStashesOff": {"label": "<PERSON><PERSON> St<PERSON>", "menus": {"gitlens/view/repositories/sections": [{"when": "!gitlens:hasVirtualFolders && config.gitlens.views.repositories.showStashes", "group": "2_git<PERSON>s", "order": 5}]}}, "gitlens.views.repositories.setShowStashesOn": {"label": "Show Stashes", "menus": {"gitlens/view/repositories/sections": [{"when": "!gitlens:hasVirtualFolders && !config.gitlens.views.repositories.showStashes", "group": "2_git<PERSON>s", "order": 5}]}}, "gitlens.views.repositories.setShowTagsOff": {"label": "Hide Tags", "menus": {"gitlens/view/repositories/sections": [{"when": "config.gitlens.views.repositories.showTags", "group": "2_git<PERSON>s", "order": 6}]}}, "gitlens.views.repositories.setShowTagsOn": {"label": "Show Tags", "menus": {"gitlens/view/repositories/sections": [{"when": "!config.gitlens.views.repositories.showTags", "group": "2_git<PERSON>s", "order": 6}]}}, "gitlens.views.repositories.setShowUpstreamStatusOff": {"label": "Hide Current Branch Status", "menus": {"gitlens/view/repositories/sections": [{"when": "config.gitlens.views.repositories.showUpstreamStatus", "group": "1_gitlens", "order": 1}]}}, "gitlens.views.repositories.setShowUpstreamStatusOn": {"label": "Show Current Branch Status", "menus": {"gitlens/view/repositories/sections": [{"when": "!config.gitlens.views.repositories.showUpstreamStatus", "group": "1_gitlens", "order": 1}]}}, "gitlens.views.repositories.setShowWorktreesOff": {"label": "<PERSON><PERSON> Worktrees", "menus": {"gitlens/view/repositories/sections": [{"when": "config.gitlens.views.repositories.showWorktrees", "group": "2_git<PERSON>s", "order": 7}]}}, "gitlens.views.repositories.setShowWorktreesOn": {"label": "Show Worktrees", "menus": {"gitlens/view/repositories/sections": [{"when": "!config.gitlens.views.repositories.showWorktrees", "group": "2_git<PERSON>s", "order": 7}]}}, "gitlens.views.repositories.viewOptionsTitle": {"label": "Repositories View Options", "enablement": "false", "menus": {"view/title": [{"when": "view == gitlens.views.scm.grouped && gitlens:views:scm:grouped:view == repositories", "group": "1_gitlens_1", "order": 1}]}}, "gitlens.views.resetCommit": {"label": "Reset Current Branch to Previous Commit...", "enablement": "!operationInProgress", "menus": {"gitlens/commit/file/commit": [{"when": "viewItem =~ /gitlens:file\\b(?=.*?\\b\\+committed\\b)/ && !gitlens:readonly && !gitlens:untrusted && !gitlens:hasVirtualFolders", "group": "1_gitlens_actions", "order": 5}], "view/item/context": [{"when": "viewItem =~ /gitlens:commit\\b(?!.*?\\b\\+rebase\\b)/ && !listMultiSelection && !gitlens:readonly && !gitlens:untrusted && !gitlens:hasVirtualFolders", "group": "1_gitlens_actions", "order": 5}]}}, "gitlens.views.resetToCommit": {"label": "Reset Current Branch to Commit...", "enablement": "!operationInProgress", "menus": {"gitlens/commit/file/commit": [{"when": "viewItem =~ /gitlens:file\\b(?=.*?\\b\\+committed\\b)/ && !gitlens:readonly && !gitlens:untrusted && !gitlens:hasVirtualFolders", "group": "1_gitlens_actions", "order": 4}], "view/item/context": [{"when": "viewItem =~ /gitlens:commit\\b(?!.*?\\b\\+rebase\\b)/ && !listMultiSelection && !gitlens:readonly && !gitlens:untrusted && !gitlens:hasVirtualFolders", "group": "1_gitlens_actions", "order": 4}]}}, "gitlens.views.resetToTip": {"label": "Reset Current Branch to Tip...", "enablement": "!operationInProgress", "menus": {"view/item/context": [{"when": "viewItem =~ /gitlens:branch\\b(?!.*?\\b\\+rebase\\b)/ && !listMultiSelection && !gitlens:readonly && !gitlens:untrusted && !gitlens:hasVirtualFolders", "group": "1_gitlens_actions_1", "order": 3}]}}, "gitlens.views.restore": {"label": "<PERSON><PERSON> (Checkout)", "icon": "$(gitlens-switch)", "menus": {"view/item/context": [{"when": "viewItem =~ /gitlens:file\\b(?=.*?\\b\\+stashed\\b)/ && !listMultiSelection && !gitlens:readonly && !gitlens:untrusted && !gitlens:hasVirtualFolders", "group": "1_gitlens_actions", "order": 3}, {"when": "viewItem =~ /gitlens:file\\b((?=.*?\\b\\+committed\\b)|:results\\b)/ && !listMultiSelection && !gitlens:readonly && !gitlens:untrusted && !gitlens:hasVirtualFolders", "group": "8_gitlens_actions", "order": 3}]}}, "gitlens.views.revealRepositoryInExplorer": {"label": "Reveal in File Explorer", "menus": {"view/item/context": [{"when": "viewItem =~ /gitlens:repository\\b(?=.*?\\b\\+workspace\\b)/ && !listMultiSelection", "group": "0_2<PERSON>lens_actions", "order": 1}, {"when": "viewItem =~ /gitlens:repository\\b(?!.*?\\b\\+workspace\\b)/ && !listMultiSelection", "group": "2_gitlens_quickopen", "order": 3}]}}, "gitlens.views.revealWorktreeInExplorer": {"label": "Reveal in File Explorer", "menus": {"view/item/context": [{"when": "viewItem =~ /gitlens:worktree\\b/ && !listMultiSelection", "group": "3_git<PERSON>s", "order": 1}]}}, "gitlens.views.revert": {"label": "Revert Commit...", "enablement": "!operationInProgress", "menus": {"gitlens/commit/file/commit": [{"when": "viewItem =~ /gitlens:file\\b(?=.*?\\b\\+committed\\b)(?=.*?\\b\\+current\\b)/ && !gitlens:readonly && !gitlens:untrusted && !gitlens:hasVirtualFolders", "group": "1_gitlens_actions", "order": 3}], "view/item/context": [{"when": "viewItem =~ /gitlens:commit\\b(?=.*?\\b\\+current\\b)/ && !listMultiSelection && !gitlens:readonly && !gitlens:untrusted && !gitlens:hasVirtualFolders", "group": "1_gitlens_actions", "order": 3}]}}, "gitlens.views.scm.grouped.attachAll": {"label": "Group All Views", "enablement": "!gitlens:views:scm:grouped:welcome", "menus": {"gitlens/views/grouped/attachOrDetach": [{"when": "view == gitlens.views.scm.grouped", "group": "9_git<PERSON>s", "order": 97}]}}, "gitlens.views.scm.grouped.branches": {"label": "Branches", "icon": "$(gitlens-branches-view)", "enablement": "!gitlens:views:scm:grouped:welcome", "menus": {"view/title": [{"when": "view == gitlens.views.scm.grouped && config.gitlens.views.scm.grouped.views.branches && !config.gitlens.views.scm.grouped.hiddenViews.branches && (gitlens:views:scm:grouped:view != branches || gitlens:views:scm:grouped:welcome)", "group": "navigation", "order": 3, "alt": "gitlens.views.scm.grouped.branches.detach"}]}, "keybindings": [{"key": "3", "when": "focusedView == gitlens.views.scm.grouped && config.gitlens.views.scm.grouped.views.branches"}]}, "gitlens.views.scm.grouped.branches.attach": {"label": "Group Branches View", "enablement": "!gitlens:views:scm:grouped:welcome", "menus": {"gitlens/views/grouped/attachOrDetach": [{"when": "view == gitlens.views.scm.grouped && !config.gitlens.views.scm.grouped.views.branches", "group": "2_git<PERSON>s", "order": 3}]}}, "gitlens.views.scm.grouped.branches.detach": {"label": "Detach Branches View", "icon": "$(gitlens-branches-view)", "menus": {"gitlens/views/grouped/attachOrDetach": [{"when": "view == gitlens.views.scm.grouped && config.gitlens.views.scm.grouped.views.branches", "group": "2_git<PERSON>s", "order": 3}], "gitlens/views/grouped/branches": [{"when": "gitlens:views:scm:grouped:view == branches", "group": "9_git<PERSON>s", "order": 3}], "view/title": [{"when": "view == gitlens.views.scm.grouped && gitlens:views:scm:grouped:view == branches && config.gitlens.views.scm.grouped.views.branches", "group": "1_gitlens", "order": -1}]}}, "gitlens.views.scm.grouped.branches.setAsDefault": {"label": "Set as Default <PERSON>", "menus": {"gitlens/views/grouped/branches": [{"when": "gitlens:views:scm:grouped:view == branches && config.gitlens.views.scm.grouped.default != branches", "group": "9_git<PERSON>s", "order": 2}]}}, "gitlens.views.scm.grouped.branches.visibility.hide": {"label": "Hide Branches View", "menus": {"gitlens/views/grouped/visibility": [{"when": "!config.gitlens.views.scm.grouped.hiddenViews.branches", "group": "2_git<PERSON>s", "order": 3}]}}, "gitlens.views.scm.grouped.branches.visibility.show": {"label": "Show Branches View", "menus": {"gitlens/views/grouped/visibility": [{"when": "config.gitlens.views.scm.grouped.hiddenViews.branches", "group": "2_git<PERSON>s", "order": 3}]}}, "gitlens.views.scm.grouped.commits": {"label": "Commits", "icon": "$(gitlens-commits-view)", "enablement": "!gitlens:views:scm:grouped:welcome", "menus": {"view/title": [{"when": "view == gitlens.views.scm.grouped && config.gitlens.views.scm.grouped.views.commits && !config.gitlens.views.scm.grouped.hiddenViews.commits && (gitlens:views:scm:grouped:view != commits || gitlens:views:scm:grouped:welcome)", "group": "navigation", "order": 1, "alt": "gitlens.views.scm.grouped.commits.detach"}]}, "keybindings": [{"key": "1", "when": "focusedView == gitlens.views.scm.grouped && config.gitlens.views.scm.grouped.views.commits"}]}, "gitlens.views.scm.grouped.commits.attach": {"label": "Group Commits View", "enablement": "!gitlens:views:scm:grouped:welcome", "menus": {"gitlens/views/grouped/attachOrDetach": [{"when": "view == gitlens.views.scm.grouped && !config.gitlens.views.scm.grouped.views.commits", "group": "2_git<PERSON>s", "order": 1}]}}, "gitlens.views.scm.grouped.commits.detach": {"label": "Detach Commits View", "icon": "$(gitlens-commits-view)", "menus": {"gitlens/views/grouped/attachOrDetach": [{"when": "view == gitlens.views.scm.grouped && config.gitlens.views.scm.grouped.views.commits", "group": "2_git<PERSON>s", "order": 1}], "gitlens/views/grouped/commits": [{"when": "gitlens:views:scm:grouped:view == commits", "group": "9_git<PERSON>s", "order": 3}], "view/title": [{"when": "view == gitlens.views.scm.grouped && gitlens:views:scm:grouped:view == commits && config.gitlens.views.scm.grouped.views.commits", "group": "1_gitlens", "order": -1}]}}, "gitlens.views.scm.grouped.commits.setAsDefault": {"label": "Set as Default <PERSON>", "menus": {"gitlens/views/grouped/commits": [{"when": "gitlens:views:scm:grouped:view == commits && config.gitlens.views.scm.grouped.default != commits", "group": "9_git<PERSON>s", "order": 2}]}}, "gitlens.views.scm.grouped.commits.visibility.hide": {"label": "Hide Commits View", "menus": {"gitlens/views/grouped/visibility": [{"when": "!config.gitlens.views.scm.grouped.hiddenViews.commits", "group": "2_git<PERSON>s", "order": 1}]}}, "gitlens.views.scm.grouped.commits.visibility.show": {"label": "Show Commits View", "menus": {"gitlens/views/grouped/visibility": [{"when": "config.gitlens.views.scm.grouped.hiddenViews.commits", "group": "2_git<PERSON>s", "order": 1}]}}, "gitlens.views.scm.grouped.contributors": {"label": "Contributors", "icon": "$(gitlens-contributors-view)", "enablement": "!gitlens:views:scm:grouped:welcome", "menus": {"view/title": [{"when": "view == gitlens.views.scm.grouped && config.gitlens.views.scm.grouped.views.contributors && !config.gitlens.views.scm.grouped.hiddenViews.contributors && (gitlens:views:scm:grouped:view != contributors || gitlens:views:scm:grouped:welcome)", "group": "navigation", "order": 7, "alt": "gitlens.views.scm.grouped.contributors.detach"}]}, "keybindings": [{"key": "7", "when": "focusedView == gitlens.views.scm.grouped && config.gitlens.views.scm.grouped.views.contributors"}]}, "gitlens.views.scm.grouped.contributors.attach": {"label": "Group Contributors View", "enablement": "!gitlens:views:scm:grouped:welcome", "menus": {"gitlens/views/grouped/attachOrDetach": [{"when": "view == gitlens.views.scm.grouped && !config.gitlens.views.scm.grouped.views.contributors", "group": "2_git<PERSON>s", "order": 7}]}}, "gitlens.views.scm.grouped.contributors.detach": {"label": "Detach Contributors View", "icon": "$(gitlens-contributors-view)", "menus": {"gitlens/views/grouped/attachOrDetach": [{"when": "view == gitlens.views.scm.grouped && config.gitlens.views.scm.grouped.views.contributors", "group": "2_git<PERSON>s", "order": 7}], "gitlens/views/grouped/contributors": [{"when": "gitlens:views:scm:grouped:view == contributors", "group": "9_git<PERSON>s", "order": 3}], "view/title": [{"when": "view == gitlens.views.scm.grouped && gitlens:views:scm:grouped:view == contributors && config.gitlens.views.scm.grouped.views.contributors", "group": "1_gitlens", "order": -1}]}}, "gitlens.views.scm.grouped.contributors.setAsDefault": {"label": "Set as Default <PERSON>", "menus": {"gitlens/views/grouped/contributors": [{"when": "gitlens:views:scm:grouped:view == contributors && config.gitlens.views.scm.grouped.default != contributors", "group": "9_git<PERSON>s", "order": 2}]}}, "gitlens.views.scm.grouped.contributors.visibility.hide": {"label": "Hide Contributors View", "menus": {"gitlens/views/grouped/visibility": [{"when": "!config.gitlens.views.scm.grouped.hiddenViews.contributors", "group": "2_git<PERSON>s", "order": 7}]}}, "gitlens.views.scm.grouped.contributors.visibility.show": {"label": "Show Contributors View", "menus": {"gitlens/views/grouped/visibility": [{"when": "config.gitlens.views.scm.grouped.hiddenViews.contributors", "group": "2_git<PERSON>s", "order": 7}]}}, "gitlens.views.scm.grouped.detachAll": {"label": "Detach All Views", "enablement": "!gitlens:views:scm:grouped:welcome", "menus": {"gitlens/views/grouped/attachOrDetach": [{"when": "view == gitlens.views.scm.grouped", "group": "9_git<PERSON>s", "order": 98}]}}, "gitlens.views.scm.grouped.fileHistory": {"label": "File History", "icon": "$(gitlens-history-view)", "enablement": "!gitlens:views:scm:grouped:welcome", "menus": {"view/title": [{"when": "view == gitlens.views.scm.grouped && config.gitlens.views.scm.grouped.views.fileHistory && !config.gitlens.views.scm.grouped.hiddenViews.fileHistory && (gitlens:views:scm:grouped:view != fileHistory || gitlens:views:scm:grouped:welcome)", "group": "navigation", "order": 9, "alt": "gitlens.views.scm.grouped.fileHistory.detach"}]}, "keybindings": [{"key": "8", "when": "focusedView == gitlens.views.scm.grouped && config.gitlens.views.scm.grouped.views.fileHistory"}]}, "gitlens.views.scm.grouped.fileHistory.attach": {"label": "Group File History View", "enablement": "!gitlens:views:scm:grouped:welcome", "menus": {"gitlens/views/grouped/attachOrDetach": [{"when": "view == gitlens.views.scm.grouped && !config.gitlens.views.scm.grouped.views.fileHistory", "group": "2_git<PERSON>s", "order": 9}]}}, "gitlens.views.scm.grouped.fileHistory.detach": {"label": "Detach File History View", "icon": "$(gitlens-history-view)", "menus": {"gitlens/views/grouped/attachOrDetach": [{"when": "view == gitlens.views.scm.grouped && config.gitlens.views.scm.grouped.views.fileHistory", "group": "2_git<PERSON>s", "order": 9}], "gitlens/views/grouped/fileHistory": [{"when": "gitlens:views:scm:grouped:view == fileHistory", "group": "9_git<PERSON>s", "order": 3}], "view/title": [{"when": "view == gitlens.views.scm.grouped && gitlens:views:scm:grouped:view == fileHistory && config.gitlens.views.scm.grouped.views.fileHistory", "group": "1_gitlens", "order": -1}]}}, "gitlens.views.scm.grouped.fileHistory.setAsDefault": {"label": "Set as Default <PERSON>", "menus": {"gitlens/views/grouped/fileHistory": [{"when": "gitlens:views:scm:grouped:view == fileHistory && config.gitlens.views.scm.grouped.default != fileHistory", "group": "9_git<PERSON>s", "order": 2}]}}, "gitlens.views.scm.grouped.fileHistory.visibility.hide": {"label": "Hide File History View", "menus": {"gitlens/views/grouped/visibility": [{"when": "!config.gitlens.views.scm.grouped.hiddenViews.fileHistory", "group": "2_git<PERSON>s", "order": 9}]}}, "gitlens.views.scm.grouped.fileHistory.visibility.show": {"label": "Show File History View", "menus": {"gitlens/views/grouped/visibility": [{"when": "config.gitlens.views.scm.grouped.hiddenViews.fileHistory", "group": "2_git<PERSON>s", "order": 9}]}}, "gitlens.views.scm.grouped.launchpad": {"label": "Launchpad", "icon": "$(gitlens-launchpad-view)", "enablement": "!gitlens:views:scm:grouped:welcome", "menus": {"view/title": [{"when": "view == gitlens.views.scm.grouped && config.gitlens.views.scm.grouped.views.launchpad && !config.gitlens.views.scm.grouped.hiddenViews.launchpad && (gitlens:views:scm:grouped:view != launchpad || gitlens:views:scm:grouped:welcome)", "group": "navigation", "order": 10, "alt": "gitlens.views.scm.grouped.launchpad.detach"}]}, "keybindings": [{"key": "9", "when": "focusedView == gitlens.views.scm.grouped && config.gitlens.views.scm.grouped.views.launchpad"}]}, "gitlens.views.scm.grouped.launchpad.attach": {"label": "Group Launchpad View", "enablement": "!gitlens:views:scm:grouped:welcome", "menus": {"gitlens/views/grouped/attachOrDetach": [{"when": "view == gitlens.views.scm.grouped && !config.gitlens.views.scm.grouped.views.launchpad", "group": "2_git<PERSON>s", "order": 10}]}}, "gitlens.views.scm.grouped.launchpad.detach": {"label": "Detach Launchpad View", "icon": "$(gitlens-launchpad-view)", "menus": {"gitlens/views/grouped/attachOrDetach": [{"when": "view == gitlens.views.scm.grouped && config.gitlens.views.scm.grouped.views.launchpad", "group": "2_git<PERSON>s", "order": 10}], "gitlens/views/grouped/launchpad": [{"when": "gitlens:views:scm:grouped:view == launchpad", "group": "9_git<PERSON>s", "order": 3}], "view/title": [{"when": "view == gitlens.views.scm.grouped && gitlens:views:scm:grouped:view == launchpad && config.gitlens.views.scm.grouped.views.launchpad", "group": "1_gitlens", "order": -1}]}}, "gitlens.views.scm.grouped.launchpad.setAsDefault": {"label": "Set as Default <PERSON>", "menus": {"gitlens/views/grouped/launchpad": [{"when": "gitlens:views:scm:grouped:view == launchpad && config.gitlens.views.scm.grouped.default != launchpad", "group": "9_git<PERSON>s", "order": 2}]}}, "gitlens.views.scm.grouped.launchpad.visibility.hide": {"label": "Hide Launchpad View", "menus": {"gitlens/views/grouped/visibility": [{"when": "!config.gitlens.views.scm.grouped.hiddenViews.launchpad", "group": "2_git<PERSON>s", "order": 10}]}}, "gitlens.views.scm.grouped.launchpad.visibility.show": {"label": "Show Launchpad View", "menus": {"gitlens/views/grouped/visibility": [{"when": "config.gitlens.views.scm.grouped.hiddenViews.launchpad", "group": "2_git<PERSON>s", "order": 10}]}}, "gitlens.views.scm.grouped.refresh": {"label": "Refresh", "icon": "$(refresh)", "enablement": "!gitlens:views:scm:grouped:welcome", "menus": {"view/title": [{"when": "view == gitlens.views.scm.grouped", "group": "navigation", "order": 98}]}}, "gitlens.views.scm.grouped.remotes": {"label": "Remotes", "icon": "$(gitlens-remotes-view)", "enablement": "!gitlens:views:scm:grouped:welcome", "menus": {"view/title": [{"when": "view == gitlens.views.scm.grouped && config.gitlens.views.scm.grouped.views.remotes && !config.gitlens.views.scm.grouped.hiddenViews.remotes && (gitlens:views:scm:grouped:view != remotes || gitlens:views:scm:grouped:welcome)", "group": "navigation", "order": 4, "alt": "gitlens.views.scm.grouped.remotes.detach"}]}, "keybindings": [{"key": "4", "when": "focusedView == gitlens.views.scm.grouped && config.gitlens.views.scm.grouped.views.remotes"}]}, "gitlens.views.scm.grouped.remotes.attach": {"label": "Group Remotes View", "enablement": "!gitlens:views:scm:grouped:welcome", "menus": {"gitlens/views/grouped/attachOrDetach": [{"when": "view == gitlens.views.scm.grouped && !config.gitlens.views.scm.grouped.views.remotes", "group": "2_git<PERSON>s", "order": 4}]}}, "gitlens.views.scm.grouped.remotes.detach": {"label": "Detach Remotes View", "icon": "$(gitlens-remotes-view)", "menus": {"gitlens/views/grouped/attachOrDetach": [{"when": "view == gitlens.views.scm.grouped && config.gitlens.views.scm.grouped.views.remotes", "group": "2_git<PERSON>s", "order": 4}], "gitlens/views/grouped/remotes": [{"when": "gitlens:views:scm:grouped:view == remotes", "group": "9_git<PERSON>s", "order": 3}], "view/title": [{"when": "view == gitlens.views.scm.grouped && gitlens:views:scm:grouped:view == remotes && config.gitlens.views.scm.grouped.views.remotes", "group": "1_gitlens", "order": -1}]}}, "gitlens.views.scm.grouped.remotes.setAsDefault": {"label": "Set as Default <PERSON>", "menus": {"gitlens/views/grouped/remotes": [{"when": "gitlens:views:scm:grouped:view == remotes && config.gitlens.views.scm.grouped.default != remotes", "group": "9_git<PERSON>s", "order": 2}]}}, "gitlens.views.scm.grouped.remotes.visibility.hide": {"label": "Hide Remotes View", "menus": {"gitlens/views/grouped/visibility": [{"when": "!config.gitlens.views.scm.grouped.hiddenViews.remotes", "group": "2_git<PERSON>s", "order": 4}]}}, "gitlens.views.scm.grouped.remotes.visibility.show": {"label": "Show Remotes View", "menus": {"gitlens/views/grouped/visibility": [{"when": "config.gitlens.views.scm.grouped.hiddenViews.remotes", "group": "2_git<PERSON>s", "order": 4}]}}, "gitlens.views.scm.grouped.repositories": {"label": "Repositories", "icon": "$(gitlens-repositories-view)", "enablement": "!gitlens:views:scm:grouped:welcome", "menus": {"view/title": [{"when": "view == gitlens.views.scm.grouped && config.gitlens.views.scm.grouped.views.repositories && !config.gitlens.views.scm.grouped.hiddenViews.repositories && (gitlens:views:scm:grouped:view != repositories || gitlens:views:scm:grouped:welcome)", "group": "navigation", "order": 8, "alt": "gitlens.views.scm.grouped.repositories.detach"}]}}, "gitlens.views.scm.grouped.repositories.attach": {"label": "Group Repositories View", "enablement": "!gitlens:views:scm:grouped:welcome", "menus": {"gitlens/views/grouped/attachOrDetach": [{"when": "view == gitlens.views.scm.grouped && !config.gitlens.views.scm.grouped.views.repositories", "group": "2_git<PERSON>s", "order": 8}]}}, "gitlens.views.scm.grouped.repositories.detach": {"label": "Detach Repositories View", "icon": "$(gitlens-repositories-view)", "menus": {"gitlens/views/grouped/attachOrDetach": [{"when": "view == gitlens.views.scm.grouped && config.gitlens.views.scm.grouped.views.repositories", "group": "2_git<PERSON>s", "order": 8}], "gitlens/views/grouped/repositories": [{"when": "gitlens:views:scm:grouped:view == repositories", "group": "9_git<PERSON>s", "order": 3}], "view/title": [{"when": "view == gitlens.views.scm.grouped && gitlens:views:scm:grouped:view == repositories && config.gitlens.views.scm.grouped.views.repositories", "group": "1_gitlens", "order": -1}]}}, "gitlens.views.scm.grouped.repositories.setAsDefault": {"label": "Set as Default <PERSON>", "menus": {"gitlens/views/grouped/repositories": [{"when": "gitlens:views:scm:grouped:view == repositories && config.gitlens.views.scm.grouped.default != repositories", "group": "9_git<PERSON>s", "order": 2}]}}, "gitlens.views.scm.grouped.repositories.visibility.hide": {"label": "Hide Repositories View", "menus": {"gitlens/views/grouped/visibility": [{"when": "!config.gitlens.views.scm.grouped.hiddenViews.repositories", "group": "2_git<PERSON>s", "order": 8}]}}, "gitlens.views.scm.grouped.repositories.visibility.show": {"label": "Show Repositories View", "menus": {"gitlens/views/grouped/visibility": [{"when": "config.gitlens.views.scm.grouped.hiddenViews.repositories", "group": "2_git<PERSON>s", "order": 8}]}}, "gitlens.views.scm.grouped.resetAll": {"label": "Reset All Views", "enablement": "!gitlens:views:scm:grouped:welcome", "menus": {"gitlens/views/grouped/attachOrDetach": [{"when": "view == gitlens.views.scm.grouped", "group": "9_git<PERSON>s", "order": 99}]}}, "gitlens.views.scm.grouped.searchAndCompare": {"label": "Search & Compare", "icon": "$(gitlens-search-view)", "enablement": "!gitlens:views:scm:grouped:welcome", "menus": {"view/title": [{"when": "view == gitlens.views.scm.grouped && config.gitlens.views.scm.grouped.views.searchAndCompare && !config.gitlens.views.scm.grouped.hiddenViews.searchAndCompare && (gitlens:views:scm:grouped:view != searchAndCompare || gitlens:views:scm:grouped:welcome)", "group": "navigation", "order": 11, "alt": "gitlens.views.scm.grouped.searchAndCompare.detach"}]}, "keybindings": [{"key": "0", "when": "focusedView == gitlens.views.scm.grouped && config.gitlens.views.scm.grouped.views.searchAndCompare"}]}, "gitlens.views.scm.grouped.searchAndCompare.attach": {"label": "Group Search & Compare View", "enablement": "!gitlens:views:scm:grouped:welcome", "menus": {"gitlens/views/grouped/attachOrDetach": [{"when": "view == gitlens.views.scm.grouped && !config.gitlens.views.scm.grouped.views.searchAndCompare", "group": "2_git<PERSON>s", "order": 11}]}}, "gitlens.views.scm.grouped.searchAndCompare.detach": {"label": "Detach Search & Compare View", "icon": "$(gitlens-search-view)", "menus": {"gitlens/views/grouped/attachOrDetach": [{"when": "view == gitlens.views.scm.grouped && config.gitlens.views.scm.grouped.views.searchAndCompare", "group": "2_git<PERSON>s", "order": 11}], "gitlens/views/grouped/searchAndCompare": [{"when": "gitlens:views:scm:grouped:view == searchAndCompare", "group": "9_git<PERSON>s", "order": 3}], "view/title": [{"when": "view == gitlens.views.scm.grouped && gitlens:views:scm:grouped:view == searchAndCompare && config.gitlens.views.scm.grouped.views.searchAndCompare", "group": "1_gitlens", "order": -1}]}}, "gitlens.views.scm.grouped.searchAndCompare.setAsDefault": {"label": "Set as Default <PERSON>", "menus": {"gitlens/views/grouped/searchAndCompare": [{"when": "gitlens:views:scm:grouped:view == searchAndCompare && config.gitlens.views.scm.grouped.default != searchAndCompare", "group": "9_git<PERSON>s", "order": 2}]}}, "gitlens.views.scm.grouped.searchAndCompare.visibility.hide": {"label": "Hide Search & Compare View", "menus": {"gitlens/views/grouped/visibility": [{"when": "!config.gitlens.views.scm.grouped.hiddenViews.searchAndCompare", "group": "2_git<PERSON>s", "order": 11}]}}, "gitlens.views.scm.grouped.searchAndCompare.visibility.show": {"label": "Show Search & Compare View", "menus": {"gitlens/views/grouped/visibility": [{"when": "config.gitlens.views.scm.grouped.hiddenViews.searchAndCompare", "group": "2_git<PERSON>s", "order": 11}]}}, "gitlens.views.scm.grouped.stashes": {"label": "Stashes", "icon": "$(gitlens-stashes-view)", "enablement": "!gitlens:views:scm:grouped:welcome", "menus": {"view/title": [{"when": "!gitlens:hasVirtualFolders && view == gitlens.views.scm.grouped && config.gitlens.views.scm.grouped.views.stashes && !config.gitlens.views.scm.grouped.hiddenViews.stashes && (gitlens:views:scm:grouped:view != stashes || gitlens:views:scm:grouped:welcome)", "group": "navigation", "order": 4, "alt": "gitlens.views.scm.grouped.stashes.detach"}]}, "keybindings": [{"key": "5", "when": "focusedView == gitlens.views.scm.grouped && config.gitlens.views.scm.grouped.views.stashes"}]}, "gitlens.views.scm.grouped.stashes.attach": {"label": "Group Stashes View", "enablement": "!gitlens:views:scm:grouped:welcome", "menus": {"gitlens/views/grouped/attachOrDetach": [{"when": "view == gitlens.views.scm.grouped && !gitlens:hasVirtualFolders && !config.gitlens.views.scm.grouped.views.stashes", "group": "2_git<PERSON>s", "order": 5}]}}, "gitlens.views.scm.grouped.stashes.detach": {"label": "Detach Stashes View", "icon": "$(gitlens-stashes-view)", "menus": {"gitlens/views/grouped/attachOrDetach": [{"when": "view == gitlens.views.scm.grouped && config.gitlens.views.scm.grouped.views.stashes", "group": "2_git<PERSON>s", "order": 5}], "gitlens/views/grouped/stashes": [{"when": "gitlens:views:scm:grouped:view == stashes", "group": "9_git<PERSON>s", "order": 3}], "view/title": [{"when": "view == gitlens.views.scm.grouped && gitlens:views:scm:grouped:view == stashes && config.gitlens.views.scm.grouped.views.stashes", "group": "1_gitlens", "order": -1}]}}, "gitlens.views.scm.grouped.stashes.setAsDefault": {"label": "Set as Default <PERSON>", "menus": {"gitlens/views/grouped/stashes": [{"when": "gitlens:views:scm:grouped:view == stashes && config.gitlens.views.scm.grouped.default != stashes", "group": "9_git<PERSON>s", "order": 2}]}}, "gitlens.views.scm.grouped.stashes.visibility.hide": {"label": "Hide Stashes View", "menus": {"gitlens/views/grouped/visibility": [{"when": "!config.gitlens.views.scm.grouped.hiddenViews.stashes", "group": "2_git<PERSON>s", "order": 5}]}}, "gitlens.views.scm.grouped.stashes.visibility.show": {"label": "Show Stashes View", "menus": {"gitlens/views/grouped/visibility": [{"when": "config.gitlens.views.scm.grouped.hiddenViews.stashes", "group": "2_git<PERSON>s", "order": 5}]}}, "gitlens.views.scm.grouped.tags": {"label": "Tags", "icon": "$(gitlens-tags-view)", "enablement": "!gitlens:views:scm:grouped:welcome", "menus": {"view/title": [{"when": "view == gitlens.views.scm.grouped && config.gitlens.views.scm.grouped.views.tags && !config.gitlens.views.scm.grouped.hiddenViews.tags && (gitlens:views:scm:grouped:view != tags || gitlens:views:scm:grouped:welcome)", "group": "navigation", "order": 6, "alt": "gitlens.views.scm.grouped.tags.detach"}]}, "keybindings": [{"key": "6", "when": "focusedView == gitlens.views.scm.grouped && config.gitlens.views.scm.grouped.views.tags"}]}, "gitlens.views.scm.grouped.tags.attach": {"label": "Group Tags View", "enablement": "!gitlens:views:scm:grouped:welcome", "menus": {"gitlens/views/grouped/attachOrDetach": [{"when": "view == gitlens.views.scm.grouped && !config.gitlens.views.scm.grouped.views.tags", "group": "2_git<PERSON>s", "order": 6}]}}, "gitlens.views.scm.grouped.tags.detach": {"label": "Detach Tags View", "icon": "$(gitlens-tags-view)", "menus": {"gitlens/views/grouped/attachOrDetach": [{"when": "view == gitlens.views.scm.grouped && config.gitlens.views.scm.grouped.views.tags", "group": "2_git<PERSON>s", "order": 6}], "gitlens/views/grouped/tags": [{"when": "gitlens:views:scm:grouped:view == tags", "group": "9_git<PERSON>s", "order": 3}], "view/title": [{"when": "view == gitlens.views.scm.grouped && gitlens:views:scm:grouped:view == tags && config.gitlens.views.scm.grouped.views.tags", "group": "1_gitlens", "order": -1}]}}, "gitlens.views.scm.grouped.tags.setAsDefault": {"label": "Set as Default <PERSON>", "menus": {"gitlens/views/grouped/tags": [{"when": "gitlens:views:scm:grouped:view == tags && config.gitlens.views.scm.grouped.default != tags", "group": "9_git<PERSON>s", "order": 2}]}}, "gitlens.views.scm.grouped.tags.visibility.hide": {"label": "Hide Tags View", "menus": {"gitlens/views/grouped/visibility": [{"when": "!config.gitlens.views.scm.grouped.hiddenViews.tags", "group": "2_git<PERSON>s", "order": 6}]}}, "gitlens.views.scm.grouped.tags.visibility.show": {"label": "Show Tags View", "menus": {"gitlens/views/grouped/visibility": [{"when": "config.gitlens.views.scm.grouped.hiddenViews.tags", "group": "2_git<PERSON>s", "order": 6}]}}, "gitlens.views.scm.grouped.worktrees": {"label": "Worktrees", "icon": "$(gitlens-worktrees-view)", "enablement": "!gitlens:views:scm:grouped:welcome", "menus": {"view/title": [{"when": "!gitlens:hasVirtualFolders && !gitlens:plus:disabled && view == gitlens.views.scm.grouped && config.gitlens.views.scm.grouped.views.worktrees && !config.gitlens.views.scm.grouped.hiddenViews.worktrees && (gitlens:views:scm:grouped:view != worktrees || gitlens:views:scm:grouped:welcome)", "group": "navigation", "order": 2, "alt": "gitlens.views.scm.grouped.worktrees.detach"}]}, "keybindings": [{"key": "2", "when": "focusedView == gitlens.views.scm.grouped && config.gitlens.views.scm.grouped.views.worktrees"}]}, "gitlens.views.scm.grouped.worktrees.attach": {"label": "Group Worktrees View", "enablement": "!gitlens:views:scm:grouped:welcome", "menus": {"gitlens/views/grouped/attachOrDetach": [{"when": "view == gitlens.views.scm.grouped && !gitlens:hasVirtualFolders && !gitlens:plus:disabled && !config.gitlens.views.scm.grouped.views.worktrees", "group": "2_git<PERSON>s", "order": 2}]}}, "gitlens.views.scm.grouped.worktrees.detach": {"label": "Detach Worktrees View", "icon": "$(gitlens-worktrees-view)", "menus": {"gitlens/views/grouped/attachOrDetach": [{"when": "view == gitlens.views.scm.grouped && !gitlens:hasVirtualFolders && !gitlens:plus:disabled && config.gitlens.views.scm.grouped.views.worktrees", "group": "2_git<PERSON>s", "order": 2}], "gitlens/views/grouped/worktrees": [{"when": "gitlens:views:scm:grouped:view == worktrees", "group": "9_git<PERSON>s", "order": 3}], "view/title": [{"when": "view == gitlens.views.scm.grouped && gitlens:views:scm:grouped:view == worktrees && config.gitlens.views.scm.grouped.views.worktrees", "group": "1_gitlens", "order": -1}]}}, "gitlens.views.scm.grouped.worktrees.setAsDefault": {"label": "Set as Default <PERSON>", "menus": {"gitlens/views/grouped/worktrees": [{"when": "gitlens:views:scm:grouped:view == worktrees && config.gitlens.views.scm.grouped.default != workspaces", "group": "9_git<PERSON>s", "order": 2}]}}, "gitlens.views.scm.grouped.worktrees.visibility.hide": {"label": "Hide Worktrees View", "menus": {"gitlens/views/grouped/visibility": [{"when": "!config.gitlens.views.scm.grouped.hiddenViews.worktrees", "group": "2_git<PERSON>s", "order": 2}]}}, "gitlens.views.scm.grouped.worktrees.visibility.show": {"label": "Show Worktrees View", "menus": {"gitlens/views/grouped/visibility": [{"when": "config.gitlens.views.scm.grouped.hiddenViews.worktrees", "group": "2_git<PERSON>s", "order": 2}]}}, "gitlens.views.searchAndCompare.attach": {"label": "Group into GitLens View", "icon": "$(close)", "menus": {"view/title": [{"when": "view == gitlens.views.searchAndCompare", "group": "navigation", "order": 99}, {"when": "view == gitlens.views.searchAndCompare", "group": "9_git<PERSON>s", "order": 0}]}}, "gitlens.views.searchAndCompare.clear": {"label": "Clear Results", "icon": "$(clear-all)", "menus": {"gitlens/views/grouped/searchAndCompare": [{"when": "gitlens:views:scm:grouped:view == searchAndCompare", "group": "2_gitlens_actions_", "order": 1}], "view/title": [{"when": "view == gitlens.views.searchAndCompare", "group": "navigation", "order": 98}]}}, "gitlens.views.searchAndCompare.copy": {"label": "Copy", "keybindings": [{"key": "ctrl+c", "when": "focusedView == gitlens.views.searchAndCompare", "mac": "cmd+c"}]}, "gitlens.views.searchAndCompare.refresh": {"label": "Refresh", "icon": "$(refresh)", "menus": {"view/title": [{"when": "view == gitlens.views.searchAndCompare", "group": "navigation", "order": 98}]}}, "gitlens.views.searchAndCompare.searchCommits": {"label": "Search Commits...", "icon": "$(search)", "menus": {"gitlens/view/searchAndCompare/new": [{"when": "view == gitlens.views.searchAndCompare || (view === gitlens.views.scm.grouped && gitlens:views:scm:grouped:view == searchAndCompare)", "group": "navigation", "order": 10}], "gitlens/views/grouped/searchAndCompare": [{"when": "view == gitlens.views.searchAndCompare || (view === gitlens.views.scm.grouped && gitlens:views:scm:grouped:view == searchAndCompare)", "group": "2_gitlens_actions", "order": 1}]}}, "gitlens.views.searchAndCompare.selectForCompare": {"label": "Compare References...", "icon": "$(compare-changes)", "menus": {"gitlens/view/searchAndCompare/new": [{"when": "view == gitlens.views.searchAndCompare || (view === gitlens.views.scm.grouped && gitlens:views:scm:grouped:view == searchAndCompare)", "group": "navigation", "order": 11}], "gitlens/views/grouped/searchAndCompare": [{"when": "view == gitlens.views.searchAndCompare || (view === gitlens.views.scm.grouped && gitlens:views:scm:grouped:view == searchAndCompare)", "group": "2_gitlens_actions", "order": 2}]}}, "gitlens.views.searchAndCompare.setFilesLayoutToAuto": {"label": "View Files as Auto", "icon": "$(list-tree)", "menus": {"gitlens/views/grouped/more": [{"when": "gitlens:views:scm:grouped:view == searchAndCompare && view == gitlens.views.scm.grouped && config.gitlens.views.searchAndCompare.files.layout == tree", "group": "3_git<PERSON>s", "order": 1}], "view/title": [{"when": "view == gitlens.views.scm.grouped && gitlens:views:scm:grouped:view == searchAndCompare && config.gitlens.views.searchAndCompare.files.layout == tree", "group": "3_git<PERSON>s", "order": 1}, {"when": "view == gitlens.views.searchAndCompare && config.gitlens.views.searchAndCompare.files.layout == tree", "group": "3_git<PERSON>s", "order": 1}]}}, "gitlens.views.searchAndCompare.setFilesLayoutToList": {"label": "View Files as List", "icon": "$(gitlens-list-auto)", "menus": {"gitlens/views/grouped/more": [{"when": "gitlens:views:scm:grouped:view == searchAndCompare && view == gitlens.views.scm.grouped && config.gitlens.views.searchAndCompare.files.layout == auto", "group": "3_git<PERSON>s", "order": 1}], "view/title": [{"when": "view == gitlens.views.scm.grouped && gitlens:views:scm:grouped:view == searchAndCompare && config.gitlens.views.searchAndCompare.files.layout == auto", "group": "3_git<PERSON>s", "order": 1}, {"when": "view == gitlens.views.searchAndCompare && config.gitlens.views.searchAndCompare.files.layout == auto", "group": "3_git<PERSON>s", "order": 1}]}}, "gitlens.views.searchAndCompare.setFilesLayoutToTree": {"label": "View Files as Tree", "icon": "$(list-flat)", "menus": {"gitlens/views/grouped/more": [{"when": "gitlens:views:scm:grouped:view == searchAndCompare && view == gitlens.views.scm.grouped && config.gitlens.views.searchAndCompare.files.layout == list", "group": "3_git<PERSON>s", "order": 1}], "view/title": [{"when": "view == gitlens.views.scm.grouped && gitlens:views:scm:grouped:view == searchAndCompare && config.gitlens.views.searchAndCompare.files.layout == list", "group": "3_git<PERSON>s", "order": 1}, {"when": "view == gitlens.views.searchAndCompare && config.gitlens.views.searchAndCompare.files.layout == list", "group": "3_git<PERSON>s", "order": 1}]}}, "gitlens.views.searchAndCompare.setShowAvatarsOff": {"label": "Hide Avatars", "menus": {"gitlens/views/grouped/more": [{"when": "gitlens:views:scm:grouped:view == searchAndCompare && view == gitlens.views.scm.grouped && config.gitlens.views.searchAndCompare.avatars", "group": "5_gitlens", "order": 1}], "view/title": [{"when": "view == gitlens.views.scm.grouped && gitlens:views:scm:grouped:view == searchAndCompare && config.gitlens.views.searchAndCompare.avatars", "group": "5_gitlens", "order": 1}, {"when": "view == gitlens.views.searchAndCompare && config.gitlens.views.searchAndCompare.avatars", "group": "5_gitlens", "order": 1}]}}, "gitlens.views.searchAndCompare.setShowAvatarsOn": {"label": "Show Avatars", "menus": {"gitlens/views/grouped/more": [{"when": "gitlens:views:scm:grouped:view == searchAndCompare && view == gitlens.views.scm.grouped && !config.gitlens.views.searchAndCompare.avatars", "group": "5_gitlens", "order": 1}], "view/title": [{"when": "view == gitlens.views.scm.grouped && gitlens:views:scm:grouped:view == searchAndCompare && !config.gitlens.views.searchAndCompare.avatars", "group": "5_gitlens", "order": 1}, {"when": "view == gitlens.views.searchAndCompare && !config.gitlens.views.searchAndCompare.avatars", "group": "5_gitlens", "order": 1}]}}, "gitlens.views.searchAndCompare.swapComparison": {"label": "Swap Comparison", "icon": "$(arrow-swap)", "enablement": "viewItem =~ /gitlens:compare:results(?!:)\\b(?!.*?\\b\\+working\\b)/", "menus": {"view/item/context": [{"when": "viewItem =~ /gitlens:compare:results(?!:)\\b/", "group": "inline", "order": 96}, {"when": "viewItem =~ /gitlens:compare:results(?!:)\\b(?!.*?\\b\\+working\\b)/ && !listMultiSelection", "group": "1_gitlens_actions", "order": 2}]}}, "gitlens.views.searchAndCompare.viewOptionsTitle": {"label": "Search & Compare View Options", "enablement": "false", "menus": {"view/title": [{"when": "view == gitlens.views.scm.grouped && gitlens:views:scm:grouped:view == searchAndCompare", "group": "1_gitlens_1", "order": 1}]}}, "gitlens.views.selectFileForCompare": {"label": "Select for Compare", "menus": {"view/item/context": [{"when": "viewItem =~ /gitlens:file\\b(?!.*?\\b\\+conflicted\\b)/ && !listMultiSelection", "group": "4_gitlens_compare", "order": 99}]}}, "gitlens.views.selectForCompare": {"label": "Select for Compare", "menus": {"view/item/context": [{"when": "viewItem =~ /gitlens:(branch|commit|stash|tag)\\b/ && !listMultiSelection", "group": "4_gitlens_compare", "order": 99}]}}, "gitlens.views.setAsDefault": {"label": "Set as <PERSON><PERSON><PERSON>", "menus": {"view/item/context": [{"when": "viewItem =~ /gitlens:remote\\b(?!.*?\\b\\+default\\b)/ && !listMultiSelection", "group": "8_gitlens_actions", "order": 1}]}}, "gitlens.views.setBranchComparisonToBranch": {"label": "Compare with Branch (HEAD)", "icon": "$(compare-changes)", "menus": {"view/item/context": [{"when": "viewItem =~ /gitlens:compare:branch\\b(?=.*?\\b\\+root\\b)(?=.*?\\b\\+current\\b)(?=.*?\\b\\+working\\b)/", "group": "inline", "order": 2}, {"when": "viewItem =~ /gitlens:compare:branch\\b(?=.*?\\b\\+root\\b)(?=.*?\\b\\+current\\b)(?=.*?\\b\\+working\\b)/ && !listMultiSelection", "group": "1_gitlens", "order": 1}]}}, "gitlens.views.setBranchComparisonToWorking": {"label": "Compare with Working Tree", "icon": "$(compare-changes)", "menus": {"view/item/context": [{"when": "viewItem =~ /gitlens:compare:branch\\b(?=.*?\\b\\+root\\b)(?=.*?\\b\\+current\\b)(?=.*?\\b\\+branch\\b)/", "group": "inline", "order": 2}, {"when": "viewItem =~ /gitlens:compare:branch\\b(?=.*?\\b\\+root\\b)(?=.*?\\b\\+current\\b)(?=.*?\\b\\+branch\\b)/ && !listMultiSelection", "group": "1_gitlens", "order": 1}]}}, "gitlens.views.setContributorsStatisticsOff": {"label": "Hide Contributors Statistics", "icon": "$(graph-line)", "menus": {"view/item/context": [{"when": "viewItem == gitlens:contributors && config.gitlens.views.showContributorsStatistics", "group": "inline", "order": 99}]}}, "gitlens.views.setContributorsStatisticsOn": {"label": "Show Contributors Statistics", "icon": "$(graph-line)", "menus": {"view/item/context": [{"when": "viewItem == gitlens:contributors && !config.gitlens.views.showContributorsStatistics", "group": "inline", "order": 99}]}}, "gitlens.views.setResultsCommitsFilterAuthors": {"label": "Filter Commits by Author...", "icon": "$(filter)", "menus": {"view/item/context": [{"when": "viewItem =~ /gitlens:compare:(results(?!:)|branch)\\b/ && !listMultiSelection", "group": "7_gitlens_filter", "order": 2}]}}, "gitlens.views.setResultsCommitsFilterOff": {"label": "Clear Filter", "icon": "$(filter-filled)", "menus": {"view/item/context": [{"when": "viewItem =~ /gitlens:compare:(results(?!:)|branch)\\b(?=.*?\\b\\+filtered\\b)/", "group": "inline", "order": 96}, {"when": "viewItem =~ /gitlens:compare:(results(?!:)|branch)\\b(?=.*?\\b\\+filtered\\b)/ && !listMultiSelection", "group": "7_gitlens_filter", "order": 1}]}}, "gitlens.views.setResultsFilesFilterOff": {"label": "Clear Filter", "menus": {"gitlens/comparison/results/files/filter": [{"when": "viewItem =~ /gitlens:results:files\\b(?=.*?\\b\\+filtered\\b)/", "group": "navigation", "order": 1}], "gitlens/comparison/results/files/filter/inline": [{"when": "viewItem =~ /gitlens:results:files\\b(?=.*?\\b\\+filtered\\b)/", "group": "navigation", "order": 1}], "gitlens/comparison/results/files/filtered": [{"when": "viewItem =~ /gitlens:results:files\\b(?=.*?\\b\\+filtered\\b)/", "group": "navigation", "order": 1}], "gitlens/comparison/results/files/filtered/inline": [{"when": "viewItem =~ /gitlens:results:files\\b(?=.*?\\b\\+filtered\\b)/", "group": "navigation", "order": 1}]}}, "gitlens.views.setResultsFilesFilterOnLeft": {"label": "Show Left-side Files Only", "enablement": "viewItem =~ /gitlens:results:files\\b(?!.*?\\b\\+filtered~left\\b)/", "menus": {"gitlens/comparison/results/files/filter": [{"group": "navigation_1", "order": 1}], "gitlens/comparison/results/files/filter/inline": [{"group": "navigation_1", "order": 1}], "gitlens/comparison/results/files/filtered": [{"group": "navigation_1", "order": 1}], "gitlens/comparison/results/files/filtered/inline": [{"group": "navigation_1", "order": 1}]}}, "gitlens.views.setResultsFilesFilterOnRight": {"label": "Show Right-side Files Only", "enablement": "viewItem =~ /gitlens:results:files\\b(?!.*?\\b\\+filtered~right\\b)/", "menus": {"gitlens/comparison/results/files/filter": [{"group": "navigation_1", "order": 2}], "gitlens/comparison/results/files/filter/inline": [{"group": "navigation_1", "order": 2}], "gitlens/comparison/results/files/filtered": [{"group": "navigation_1", "order": 2}], "gitlens/comparison/results/files/filtered/inline": [{"group": "navigation_1", "order": 2}]}}, "gitlens.views.setShowRelativeDateMarkersOff": {"label": "Hide Date Markers", "menus": {"gitlens/views/grouped/more": [{"when": "gitlens:views:scm:grouped:view == branches && view == gitlens.views.scm.grouped && config.gitlens.views.showRelativeDateMarkers", "group": "5_gitlens", "order": 4}, {"when": "gitlens:views:scm:grouped:view == commits && view == gitlens.views.scm.grouped && config.gitlens.views.showRelativeDateMarkers", "group": "5_gitlens", "order": 4}, {"when": "gitlens:views:scm:grouped:view == contributors && view == gitlens.views.scm.grouped && config.gitlens.views.showRelativeDateMarkers", "group": "5_gitlens", "order": 2}, {"when": "gitlens:views:scm:grouped:view == remotes && view == gitlens.views.scm.grouped && config.gitlens.views.showRelativeDateMarkers", "group": "5_gitlens", "order": 3}, {"when": "gitlens:views:scm:grouped:view == repositories && view == gitlens.views.scm.grouped && config.gitlens.views.showRelativeDateMarkers", "group": "5_gitlens", "order": 2}, {"when": "gitlens:views:scm:grouped:view == tags && view == gitlens.views.scm.grouped && config.gitlens.views.showRelativeDateMarkers", "group": "5_gitlens", "order": 2}, {"when": "gitlens:views:scm:grouped:view == worktrees && view == gitlens.views.scm.grouped && config.gitlens.views.showRelativeDateMarkers", "group": "5_gitlens", "order": 4}], "view/item/context": [{"when": "viewItem == gitlens:date-marker && !listMultiSelection && config.gitlens.views.showRelativeDateMarkers", "group": "1_gitlens", "order": 0}], "view/title": [{"when": "view == gitlens.views.branches && config.gitlens.views.showRelativeDateMarkers", "group": "5_gitlens", "order": 4}, {"when": "view == gitlens.views.commits && config.gitlens.views.showRelativeDateMarkers", "group": "5_gitlens", "order": 4}, {"when": "view == gitlens.views.contributors && config.gitlens.views.showRelativeDateMarkers", "group": "5_gitlens", "order": 2}, {"when": "view == gitlens.views.fileHistory && config.gitlens.views.showRelativeDateMarkers", "group": "5_gitlens", "order": 2}, {"when": "view == gitlens.views.lineHistory && config.gitlens.views.showRelativeDateMarkers", "group": "5_gitlens", "order": 2}, {"when": "view == gitlens.views.remotes && config.gitlens.views.showRelativeDateMarkers", "group": "5_gitlens", "order": 3}, {"when": "view == gitlens.views.repositories && config.gitlens.views.showRelativeDateMarkers", "group": "5_gitlens", "order": 2}, {"when": "view == gitlens.views.scm.grouped && gitlens:views:scm:grouped:view == contributors && config.gitlens.views.showRelativeDateMarkers", "group": "5_gitlens", "order": 2}, {"when": "view == gitlens.views.scm.grouped && gitlens:views:scm:grouped:view == repositories && config.gitlens.views.showRelativeDateMarkers", "group": "5_gitlens", "order": 2}, {"when": "view == gitlens.views.scm.grouped && gitlens:views:scm:grouped:view == tags && config.gitlens.views.showRelativeDateMarkers", "group": "5_gitlens", "order": 2}, {"when": "view == gitlens.views.scm.grouped && gitlens:views:scm:grouped:view == remotes && config.gitlens.views.showRelativeDateMarkers", "group": "5_gitlens", "order": 3}, {"when": "view == gitlens.views.scm.grouped && gitlens:views:scm:grouped:view == branches && config.gitlens.views.showRelativeDateMarkers", "group": "5_gitlens", "order": 4}, {"when": "view == gitlens.views.scm.grouped && gitlens:views:scm:grouped:view == commits && config.gitlens.views.showRelativeDateMarkers", "group": "5_gitlens", "order": 4}, {"when": "view == gitlens.views.scm.grouped && gitlens:views:scm:grouped:view == worktrees && config.gitlens.views.showRelativeDateMarkers", "group": "5_gitlens", "order": 4}, {"when": "view == gitlens.views.tags && config.gitlens.views.showRelativeDateMarkers", "group": "5_gitlens", "order": 2}, {"when": "view == gitlens.views.worktrees && config.gitlens.views.showRelativeDateMarkers", "group": "5_gitlens", "order": 4}]}}, "gitlens.views.setShowRelativeDateMarkersOn": {"label": "Show Date Markers", "menus": {"gitlens/views/grouped/more": [{"when": "gitlens:views:scm:grouped:view == branches && view == gitlens.views.scm.grouped && !config.gitlens.views.showRelativeDateMarkers", "group": "5_gitlens", "order": 4}, {"when": "gitlens:views:scm:grouped:view == commits && view == gitlens.views.scm.grouped && !config.gitlens.views.showRelativeDateMarkers", "group": "5_gitlens", "order": 4}, {"when": "gitlens:views:scm:grouped:view == contributors && view == gitlens.views.scm.grouped && !config.gitlens.views.showRelativeDateMarkers", "group": "5_gitlens", "order": 2}, {"when": "gitlens:views:scm:grouped:view == remotes && view == gitlens.views.scm.grouped && !config.gitlens.views.showRelativeDateMarkers", "group": "5_gitlens", "order": 3}, {"when": "gitlens:views:scm:grouped:view == repositories && view == gitlens.views.scm.grouped && !config.gitlens.views.showRelativeDateMarkers", "group": "5_gitlens", "order": 2}, {"when": "gitlens:views:scm:grouped:view == tags && view == gitlens.views.scm.grouped && !config.gitlens.views.showRelativeDateMarkers", "group": "5_gitlens", "order": 2}, {"when": "gitlens:views:scm:grouped:view == worktrees && view == gitlens.views.scm.grouped && !config.gitlens.views.showRelativeDateMarkers", "group": "5_gitlens", "order": 4}], "view/title": [{"when": "view == gitlens.views.branches && !config.gitlens.views.showRelativeDateMarkers", "group": "5_gitlens", "order": 4}, {"when": "view == gitlens.views.commits && !config.gitlens.views.showRelativeDateMarkers", "group": "5_gitlens", "order": 4}, {"when": "view == gitlens.views.contributors && !config.gitlens.views.showRelativeDateMarkers", "group": "5_gitlens", "order": 2}, {"when": "view == gitlens.views.fileHistory && !config.gitlens.views.showRelativeDateMarkers", "group": "5_gitlens", "order": 2}, {"when": "view == gitlens.views.lineHistory && !config.gitlens.views.showRelativeDateMarkers", "group": "5_gitlens", "order": 2}, {"when": "view == gitlens.views.remotes && !config.gitlens.views.showRelativeDateMarkers", "group": "5_gitlens", "order": 3}, {"when": "view == gitlens.views.repositories && !config.gitlens.views.showRelativeDateMarkers", "group": "5_gitlens", "order": 2}, {"when": "view == gitlens.views.scm.grouped && gitlens:views:scm:grouped:view == contributors && !config.gitlens.views.showRelativeDateMarkers", "group": "5_gitlens", "order": 2}, {"when": "view == gitlens.views.scm.grouped && gitlens:views:scm:grouped:view == repositories && !config.gitlens.views.showRelativeDateMarkers", "group": "5_gitlens", "order": 2}, {"when": "view == gitlens.views.scm.grouped && gitlens:views:scm:grouped:view == tags && !config.gitlens.views.showRelativeDateMarkers", "group": "5_gitlens", "order": 2}, {"when": "view == gitlens.views.scm.grouped && gitlens:views:scm:grouped:view == remotes && !config.gitlens.views.showRelativeDateMarkers", "group": "5_gitlens", "order": 3}, {"when": "view == gitlens.views.scm.grouped && gitlens:views:scm:grouped:view == branches && !config.gitlens.views.showRelativeDateMarkers", "group": "5_gitlens", "order": 4}, {"when": "view == gitlens.views.scm.grouped && gitlens:views:scm:grouped:view == commits && !config.gitlens.views.showRelativeDateMarkers", "group": "5_gitlens", "order": 4}, {"when": "view == gitlens.views.scm.grouped && gitlens:views:scm:grouped:view == worktrees && !config.gitlens.views.showRelativeDateMarkers", "group": "5_gitlens", "order": 4}, {"when": "view == gitlens.views.tags && !config.gitlens.views.showRelativeDateMarkers", "group": "5_gitlens", "order": 2}, {"when": "view == gitlens.views.worktrees && !config.gitlens.views.showRelativeDateMarkers", "group": "5_gitlens", "order": 4}]}}, "gitlens.views.skipPausedOperation": {"label": "<PERSON><PERSON>", "icon": "$(debug-step-over)", "menus": {"view/item/context": [{"when": "viewItem =~ /gitlens:paused-operation:(cherry-pick|rebase|revert)\\b/ && !listMultiSelection", "group": "inline", "order": 2}, {"when": "viewItem =~ /gitlens:paused-operation:(cherry-pick|rebase|revert)\\b/ && !listMultiSelection", "group": "1_gitlens_actions", "order": 2}]}}, "gitlens.views.stageDirectory": {"label": "Stage All Changes", "icon": "$(add)", "enablement": "!operationInProgress", "menus": {"view/item/context": [{"when": "viewItem =~ /gitlens:folder\\b(?=.*?\\b\\+working\\b)/ && !listMultiSelection && !gitlens:readonly && !gitlens:untrusted && !gitlens:hasVirtualFolders", "group": "inline", "order": 1}, {"when": "viewItem =~ /gitlens:folder\\b(?=.*?\\b\\+working\\b)/ && !listMultiSelection && !gitlens:readonly && !gitlens:untrusted && !gitlens:hasVirtualFolders", "group": "1_gitlens_actions", "order": 1}]}}, "gitlens.views.stageFile": {"label": "Stage Changes", "icon": "$(add)", "enablement": "!operationInProgress", "menus": {"view/item/context": [{"when": "viewItem =~ /gitlens:file\\b(?=.*?\\b\\+unstaged\\b)/ && !gitlens:readonly && !gitlens:untrusted && !gitlens:hasVirtualFolders", "group": "inline", "order": 97}, {"when": "viewItem =~ /gitlens:file\\b(?=.*?\\b\\+unstaged\\b)/ && !listMultiSelection && !gitlens:readonly && !gitlens:untrusted && !gitlens:hasVirtualFolders", "group": "1_gitlens_actions", "order": 1}]}}, "gitlens.views.star": {"label": "Add to Favorites", "icon": "$(star-empty)", "menus": {"view/item/context": [{"when": "viewItem =~ /gitlens:branch\\b(?!.*?\\b\\+starred\\b)/ && !listMultiSelection", "group": "8_gitlens_actions", "order": 1}, {"when": "viewItem =~ /gitlens:repo-folder\\b(?!.*?\\b\\+starred\\b)/", "group": "inline", "order": 99}, {"when": "viewItem =~ /gitlens:repo-folder\\b(?!.*?\\b\\+starred\\b)/ && !listMultiSelection", "group": "8_gitlens_actions_", "order": 1}, {"when": "viewItem =~ /gitlens:repository\\b(?!.*?\\b\\+(starred|workspace)\\b)/", "group": "inline", "order": 99}, {"when": "viewItem =~ /gitlens:repository\\b(?!.*?\\b\\+(starred|workspace)\\b)/ && !listMultiSelection", "group": "8_gitlens_actions_", "order": 1}]}}, "gitlens.views.star.multi": {"label": "Add to Favorites", "icon": "$(star-empty)", "menus": {"view/item/context": [{"when": "viewItem =~ /gitlens:branch\\b(?!.*?\\b\\+starred\\b)/ && listMultiSelection", "group": "8_gitlens_actions", "order": 1}, {"when": "viewItem =~ /gitlens:repo-folder\\b(?!.*?\\b\\+starred\\b)/ && listMultiSelection", "group": "8_gitlens_actions_", "order": 1}, {"when": "viewItem =~ /gitlens:repository\\b(?!.*?\\b\\+(starred|workspace)\\b)/ && listMultiSelection", "group": "8_gitlens_actions_", "order": 1}]}}, "gitlens.views.stash.apply": {"label": "Apply Stash...", "icon": "$(gitlens-stash-pop)", "enablement": "!operationInProgress", "menus": {"view/item/context": [{"when": "viewItem == gitlens:stash && !gitlens:readonly && !gitlens:untrusted && !gitlens:hasVirtualFolders", "group": "inline", "order": 1}, {"when": "viewItem == gitlens:stash && !listMultiSelection && !gitlens:readonly && !gitlens:untrusted && !gitlens:hasVirtualFolders", "group": "1_gitlens_actions", "order": 1}]}}, "gitlens.views.stash.delete": {"label": "Drop Stash...", "icon": "$(trash)", "enablement": "!operationInProgress", "menus": {"view/item/context": [{"when": "viewItem == gitlens:stash && !gitlens:readonly && !gitlens:untrusted && !gitlens:hasVirtualFolders", "group": "inline", "order": 99}, {"when": "viewItem == gitlens:stash && !listMultiSelection && !gitlens:readonly && !gitlens:untrusted && !gitlens:hasVirtualFolders", "group": "1_gitlens_actions", "order": 3}]}}, "gitlens.views.stash.delete.multi": {"label": "Drop Stashes...", "icon": "$(trash)", "enablement": "!operationInProgress", "menus": {"view/item/context": [{"when": "viewItem == gitlens:stash && listMultiSelection && !gitlens:readonly && !gitlens:untrusted && !gitlens:hasVirtualFolders", "group": "1_gitlens_actions", "order": 3}]}}, "gitlens.views.stash.rename": {"label": "<PERSON><PERSON>...", "icon": "$(edit)", "enablement": "!operationInProgress", "menus": {"view/item/context": [{"when": "viewItem == gitlens:stash && !gitlens:readonly && !gitlens:untrusted && !gitlens:hasVirtualFolders", "group": "inline", "order": 98}, {"when": "viewItem == gitlens:stash && !listMultiSelection && !gitlens:readonly && !gitlens:untrusted && !gitlens:hasVirtualFolders", "group": "1_gitlens_actions", "order": 2}]}}, "gitlens.views.stashes.attach": {"label": "Group into GitLens View", "icon": "$(close)", "menus": {"view/title": [{"when": "view == gitlens.views.stashes", "group": "navigation", "order": 99}, {"when": "view == gitlens.views.stashes", "group": "9_git<PERSON>s", "order": 0}]}}, "gitlens.views.stashes.copy": {"label": "Copy", "keybindings": [{"key": "ctrl+c", "when": "focusedView == gitlens.views.stashes", "mac": "cmd+c"}]}, "gitlens.views.stashes.refresh": {"label": "Refresh", "icon": "$(refresh)", "menus": {"view/title": [{"when": "view == gitlens.views.stashes", "group": "navigation", "order": 98}]}}, "gitlens.views.stashes.setFilesLayoutToAuto": {"label": "View Files as Auto", "icon": "$(list-tree)", "menus": {"gitlens/views/grouped/more": [{"when": "gitlens:views:scm:grouped:view == stashes && view == gitlens.views.scm.grouped && config.gitlens.views.stashes.files.layout == tree", "group": "3_git<PERSON>s", "order": 1}], "view/title": [{"when": "view == gitlens.views.scm.grouped && gitlens:views:scm:grouped:view == stashes && config.gitlens.views.stashes.files.layout == tree", "group": "3_git<PERSON>s", "order": 1}, {"when": "view == gitlens.views.stashes && config.gitlens.views.stashes.files.layout == tree", "group": "3_git<PERSON>s", "order": 0}]}}, "gitlens.views.stashes.setFilesLayoutToList": {"label": "View Files as List", "icon": "$(gitlens-list-auto)", "menus": {"gitlens/views/grouped/more": [{"when": "gitlens:views:scm:grouped:view == stashes && view == gitlens.views.scm.grouped && config.gitlens.views.stashes.files.layout == auto", "group": "3_git<PERSON>s", "order": 1}], "view/title": [{"when": "view == gitlens.views.scm.grouped && gitlens:views:scm:grouped:view == stashes && config.gitlens.views.stashes.files.layout == auto", "group": "3_git<PERSON>s", "order": 1}, {"when": "view == gitlens.views.stashes && config.gitlens.views.stashes.files.layout == auto", "group": "3_git<PERSON>s", "order": 0}]}}, "gitlens.views.stashes.setFilesLayoutToTree": {"label": "View Files as Tree", "icon": "$(list-flat)", "menus": {"gitlens/views/grouped/more": [{"when": "gitlens:views:scm:grouped:view == stashes && view == gitlens.views.scm.grouped && config.gitlens.views.stashes.files.layout == list", "group": "3_git<PERSON>s", "order": 1}], "view/title": [{"when": "view == gitlens.views.scm.grouped && gitlens:views:scm:grouped:view == stashes && config.gitlens.views.stashes.files.layout == list", "group": "3_git<PERSON>s", "order": 1}, {"when": "view == gitlens.views.stashes && config.gitlens.views.stashes.files.layout == list", "group": "3_git<PERSON>s", "order": 0}]}}, "gitlens.views.stashes.viewOptionsTitle": {"label": "Stashes View Options", "enablement": "false", "menus": {"view/title": [{"when": "view == gitlens.views.scm.grouped && gitlens:views:scm:grouped:view == stashes", "group": "1_gitlens_1", "order": 1}]}}, "gitlens.views.switchToAnotherBranch": {"label": "Switch to Another Branch...", "icon": "$(gitlens-switch)", "enablement": "!operationInProgress", "menus": {"gitlens/views/grouped/branches": [{"when": "gitlens:views:scm:grouped:view == branches && !gitlens:readonly && !gitlens:untrusted && !gitlens:hasVirtualFolders", "group": "3_git<PERSON>s", "order": 1}], "gitlens/views/grouped/commits": [{"when": "gitlens:views:scm:grouped:view == commits && !gitlens:readonly && !gitlens:untrusted && !gitlens:hasVirtualFolders", "group": "2_gitlens_actions_", "order": 0}], "view/item/context": [{"when": "viewItem =~ /gitlens:branch\\b(?=.*?\\b\\+current\\b)(?!.*?\\b\\+closed\\b)/ && !gitlens:readonly && !gitlens:untrusted && !gitlens:hasVirtualFolders", "group": "inline", "order": 7}, {"when": "viewItem =~ /gitlens:branch\\b(?=.*?\\b\\+current\\b)(?!.*?\\b\\+closed\\b)/ && !listMultiSelection && !gitlens:readonly && !gitlens:untrusted && !gitlens:hasVirtualFolders", "group": "1_gitlens_action", "order": 1}, {"when": "viewItem =~ /gitlens:branches\\b(?!.*?\\b\\+closed\\b)/ && !gitlens:readonly && !gitlens:untrusted && !gitlens:hasVirtualFolders", "group": "inline", "order": 10}, {"when": "viewItem =~ /gitlens:branches\\b(?!.*?\\b\\+closed\\b)/ && !listMultiSelection && !gitlens:readonly && !gitlens:untrusted && !gitlens:hasVirtualFolders", "group": "1_gitlens_actions", "order": 1}], "view/title": [{"when": "view == gitlens.views.branches && !gitlens:readonly && !gitlens:untrusted && !gitlens:hasVirtualFolders", "group": "navigation", "order": 10}, {"when": "view == gitlens.views.commits && !gitlens:readonly && !gitlens:untrusted && !gitlens:hasVirtualFolders", "group": "navigation", "order": 10}]}}, "gitlens.views.switchToBranch": {"label": "Switch to Branch...", "icon": "$(gitlens-switch)", "enablement": "!operationInProgress", "menus": {"view/item/context": [{"when": "viewItem =~ /gitlens:branch\\b(?!.*?\\b\\+(current|closed|checkedout|worktree)\\b)/ && !gitlens:readonly && !gitlens:untrusted && !gitlens:hasVirtualFolders", "group": "inline", "order": 7}, {"when": "viewItem =~ /gitlens:branch\\b(?!.*?\\b\\+(current|closed|checkedout|worktree)\\b)/ && !listMultiSelection && !gitlens:readonly && !gitlens:untrusted && !gitlens:hasVirtualFolders", "group": "1_gitlens_action", "order": 1}]}}, "gitlens.views.switchToCommit": {"label": "Checkout Commit...", "icon": "$(gitlens-switch)", "enablement": "!operationInProgress", "menus": {"gitlens/commit/file/commit": [{"when": "viewItem =~ /gitlens:file\\b(?=.*?\\b\\+committed\\b)/ && !gitlens:readonly && !gitlens:untrusted && !gitlens:hasVirtualFolders", "group": "1_gitlens_actions", "order": 7}], "view/item/context": [{"when": "viewItem =~ /gitlens:commit\\b(?!.*?\\b\\+rebase\\b)/ && !listMultiSelection && !gitlens:readonly && !gitlens:untrusted && !gitlens:hasVirtualFolders", "group": "1_gitlens_actions", "order": 7}]}}, "gitlens.views.switchToTag": {"label": "Checkout Tag...", "icon": "$(gitlens-switch)", "enablement": "!operationInProgress", "menus": {"view/item/context": [{"when": "viewItem =~ /gitlens:tag\\b/ && !gitlens:readonly && !gitlens:untrusted && !gitlens:hasVirtualFolders", "group": "inline", "order": 10}, {"when": "viewItem =~ /gitlens:tag\\b/ && !listMultiSelection && !gitlens:readonly && !gitlens:untrusted && !gitlens:hasVirtualFolders", "group": "1_gitlens_actions", "order": 1}]}}, "gitlens.views.tags.attach": {"label": "Group into GitLens View", "icon": "$(close)", "menus": {"view/title": [{"when": "view == gitlens.views.tags", "group": "navigation", "order": 99}, {"when": "view == gitlens.views.tags", "group": "9_git<PERSON>s", "order": 0}]}}, "gitlens.views.tags.copy": {"label": "Copy", "keybindings": [{"key": "ctrl+c", "when": "focusedView == gitlens.views.tags", "mac": "cmd+c"}]}, "gitlens.views.tags.refresh": {"label": "Refresh", "icon": "$(refresh)", "menus": {"view/title": [{"when": "view == gitlens.views.tags", "group": "navigation", "order": 98}]}}, "gitlens.views.tags.setFilesLayoutToAuto": {"label": "View Files as Auto", "icon": "$(list-tree)", "menus": {"gitlens/views/grouped/more": [{"when": "gitlens:views:scm:grouped:view == tags && view == gitlens.views.scm.grouped && config.gitlens.views.tags.files.layout == tree", "group": "3_git<PERSON>s", "order": 2}], "view/title": [{"when": "view == gitlens.views.scm.grouped && gitlens:views:scm:grouped:view == tags && config.gitlens.views.tags.files.layout == tree", "group": "3_git<PERSON>s", "order": 2}, {"when": "view == gitlens.views.tags && config.gitlens.views.tags.files.layout == tree", "group": "3_git<PERSON>s", "order": 1}]}}, "gitlens.views.tags.setFilesLayoutToList": {"label": "View Files as List", "icon": "$(gitlens-list-auto)", "menus": {"gitlens/views/grouped/more": [{"when": "gitlens:views:scm:grouped:view == tags && view == gitlens.views.scm.grouped && config.gitlens.views.tags.files.layout == auto", "group": "3_git<PERSON>s", "order": 2}], "view/title": [{"when": "view == gitlens.views.scm.grouped && gitlens:views:scm:grouped:view == tags && config.gitlens.views.tags.files.layout == auto", "group": "3_git<PERSON>s", "order": 2}, {"when": "view == gitlens.views.tags && config.gitlens.views.tags.files.layout == auto", "group": "3_git<PERSON>s", "order": 1}]}}, "gitlens.views.tags.setFilesLayoutToTree": {"label": "View Files as Tree", "icon": "$(list-flat)", "menus": {"gitlens/views/grouped/more": [{"when": "gitlens:views:scm:grouped:view == tags && view == gitlens.views.scm.grouped && config.gitlens.views.tags.files.layout == list", "group": "3_git<PERSON>s", "order": 2}], "view/title": [{"when": "view == gitlens.views.scm.grouped && gitlens:views:scm:grouped:view == tags && config.gitlens.views.tags.files.layout == list", "group": "3_git<PERSON>s", "order": 2}, {"when": "view == gitlens.views.tags && config.gitlens.views.tags.files.layout == list", "group": "3_git<PERSON>s", "order": 1}]}}, "gitlens.views.tags.setLayoutToList": {"label": "View as List", "icon": "$(list-tree)", "menus": {"gitlens/views/grouped/more": [{"when": "gitlens:views:scm:grouped:view == tags && view == gitlens.views.scm.grouped && config.gitlens.views.tags.branches.layout == tree", "group": "3_git<PERSON>s", "order": 1}], "view/title": [{"when": "view == gitlens.views.scm.grouped && gitlens:views:scm:grouped:view == tags && config.gitlens.views.tags.branches.layout == tree", "group": "3_git<PERSON>s", "order": 1}, {"when": "view == gitlens.views.tags && config.gitlens.views.tags.branches.layout == tree", "group": "navigation", "order": 50}]}}, "gitlens.views.tags.setLayoutToTree": {"label": "View as Tree", "icon": "$(list-flat)", "menus": {"gitlens/views/grouped/more": [{"when": "gitlens:views:scm:grouped:view == tags && view == gitlens.views.scm.grouped && config.gitlens.views.tags.branches.layout == list", "group": "3_git<PERSON>s", "order": 1}], "view/title": [{"when": "view == gitlens.views.scm.grouped && gitlens:views:scm:grouped:view == tags && config.gitlens.views.tags.branches.layout == list", "group": "3_git<PERSON>s", "order": 1}, {"when": "view == gitlens.views.tags && config.gitlens.views.tags.branches.layout == list", "group": "navigation", "order": 50}]}}, "gitlens.views.tags.setShowAvatarsOff": {"label": "Hide Avatars", "menus": {"gitlens/views/grouped/more": [{"when": "gitlens:views:scm:grouped:view == tags && view == gitlens.views.scm.grouped && config.gitlens.views.tags.avatars", "group": "5_gitlens", "order": 1}], "view/title": [{"when": "view == gitlens.views.scm.grouped && gitlens:views:scm:grouped:view == tags && config.gitlens.views.tags.avatars", "group": "5_gitlens", "order": 1}, {"when": "view == gitlens.views.tags && config.gitlens.views.tags.avatars", "group": "5_gitlens", "order": 1}]}}, "gitlens.views.tags.setShowAvatarsOn": {"label": "Show Avatars", "menus": {"gitlens/views/grouped/more": [{"when": "gitlens:views:scm:grouped:view == tags && view == gitlens.views.scm.grouped && !config.gitlens.views.tags.avatars", "group": "5_gitlens", "order": 1}], "view/title": [{"when": "view == gitlens.views.scm.grouped && gitlens:views:scm:grouped:view == tags && !config.gitlens.views.tags.avatars", "group": "5_gitlens", "order": 1}, {"when": "view == gitlens.views.tags && !config.gitlens.views.tags.avatars", "group": "5_gitlens", "order": 1}]}}, "gitlens.views.tags.viewOptionsTitle": {"label": "Tags View Options", "enablement": "false", "menus": {"view/title": [{"when": "view == gitlens.views.scm.grouped && gitlens:views:scm:grouped:view == tags", "group": "1_gitlens_1", "order": 1}]}}, "gitlens.views.timeline.refresh": {"label": "Refresh", "icon": "$(refresh)", "menus": {"view/title": [{"when": "view == gitlens.views.timeline", "group": "navigation", "order": 99}]}}, "gitlens.views.title.createBranch": {"label": "Create Branch...", "icon": "$(add)", "enablement": "!operationInProgress", "menus": {"gitlens/views/grouped/branches": [{"when": "gitlens:views:scm:grouped:view == branches && !gitlens:readonly && !gitlens:untrusted && !gitlens:hasVirtualFolders", "group": "3_git<PERSON>s", "order": 2}], "view/title": [{"when": "view == gitlens.views.branches && !gitlens:readonly && !gitlens:untrusted && !gitlens:hasVirtualFolders", "group": "navigation", "order": 11}]}}, "gitlens.views.title.createTag": {"label": "Create Tag...", "icon": "$(add)", "enablement": "!operationInProgress", "menus": {"gitlens/views/grouped/tags": [{"when": "gitlens:views:scm:grouped:view == tags && !gitlens:readonly && !gitlens:untrusted && !gitlens:hasVirtualFolders", "group": "2_gitlens_actions", "order": 1}], "view/title": [{"when": "view == gitlens.views.tags && !gitlens:readonly && !gitlens:untrusted && !gitlens:hasVirtualFolders", "group": "navigation", "order": 10}]}}, "gitlens.views.title.createWorktree": {"label": "Create Worktree...", "icon": "$(add)", "enablement": "!operationInProgress", "menus": {"gitlens/views/grouped/worktrees": [{"when": "gitlens:views:scm:grouped:view == worktrees", "group": "2_gitlens_actions", "order": 1}], "view/title": [{"when": "view == gitlens.views.worktrees", "group": "navigation", "order": 10}]}}, "gitlens.views.undoCommit": {"label": "Undo Commit", "icon": "$(discard)", "enablement": "!operationInProgress", "menus": {"gitlens/commit/file/commit": [{"when": "viewItem =~ /gitlens:file\\b(?=.*?\\b\\+committed\\b)(?=.*?\\b\\+HEAD\\b)/ && !gitlens:readonly && !gitlens:untrusted && !gitlens:hasVirtualFolders", "group": "1_gitlens_actions", "order": 0}], "view/item/context": [{"when": "viewItem =~ /gitlens:commit\\b(?=.*?\\b\\+HEAD\\b)/ && !gitlens:readonly && !gitlens:untrusted && !gitlens:hasVirtualFolders", "group": "inline", "order": 78}, {"when": "viewItem =~ /gitlens:commit\\b(?=.*?\\b\\+HEAD\\b)/ && !listMultiSelection && !gitlens:readonly && !gitlens:untrusted && !gitlens:hasVirtualFolders", "group": "1_gitlens_actions", "order": 1}]}}, "gitlens.views.unsetAsDefault": {"label": "Unset as <PERSON><PERSON><PERSON>", "menus": {"view/item/context": [{"when": "viewItem =~ /gitlens:remote\\b(?=.*?\\b\\+default\\b)/ && !listMultiSelection", "group": "8_gitlens_actions", "order": 1}]}}, "gitlens.views.unstageDirectory": {"label": "Unstage All Changes", "icon": "$(remove)", "enablement": "!operationInProgress", "menus": {"view/item/context": [{"when": "viewItem =~ /gitlens:folder\\b(?=.*?\\b\\+working\\b)/ && !listMultiSelection && !gitlens:readonly && !gitlens:untrusted && !gitlens:hasVirtualFolders", "group": "inline", "order": 2}, {"when": "viewItem =~ /gitlens:folder\\b(?=.*?\\b\\+working\\b)/ && !listMultiSelection && !gitlens:readonly && !gitlens:untrusted && !gitlens:hasVirtualFolders", "group": "1_gitlens_actions", "order": 2}]}}, "gitlens.views.unstageFile": {"label": "Unstage Changes", "icon": "$(remove)", "enablement": "!operationInProgress", "menus": {"view/item/context": [{"when": "viewItem =~ /gitlens:file\\b(?=.*?\\b\\+staged\\b)/ && !gitlens:readonly && !gitlens:untrusted && !gitlens:hasVirtualFolders", "group": "inline", "order": 97}, {"when": "viewItem =~ /gitlens:file\\b(?=.*?\\b\\+staged\\b)/ && !listMultiSelection && !gitlens:readonly && !gitlens:untrusted && !gitlens:hasVirtualFolders", "group": "1_gitlens_actions", "order": 1}]}}, "gitlens.views.unstar": {"label": "Remove from Favorites", "icon": "$(star-full)", "menus": {"view/item/context": [{"when": "viewItem =~ /gitlens:branch\\b(?=.*?\\b\\+starred\\b)/ && !listMultiSelection", "group": "8_gitlens_actions", "order": 1}, {"when": "viewItem =~ /gitlens:repo-folder\\b(?=.*?\\b\\+starred\\b)/", "group": "inline", "order": 99}, {"when": "viewItem =~ /gitlens:repo-folder\\b(?=.*?\\b\\+starred\\b)/ && !listMultiSelection", "group": "8_gitlens_actions_", "order": 1}, {"when": "viewItem =~ /gitlens:repository\\b(?=.*?\\b\\+starred\\b)/", "group": "inline", "order": 99}, {"when": "viewItem =~ /gitlens:repository\\b(?=.*?\\b\\+starred\\b)/ && !listMultiSelection", "group": "8_gitlens_actions_", "order": 1}]}}, "gitlens.views.unstar.multi": {"label": "Remove from Favorites", "icon": "$(star-full)", "menus": {"view/item/context": [{"when": "viewItem =~ /gitlens:branch\\b(?=.*?\\b\\+starred\\b)/ && listMultiSelection", "group": "8_gitlens_actions", "order": 1}, {"when": "viewItem =~ /gitlens:repo-folder\\b(?=.*?\\b\\+starred\\b)/ && listMultiSelection", "group": "8_gitlens_actions_", "order": 1}, {"when": "viewItem =~ /gitlens:repository\\b(?=.*?\\b\\+starred\\b)/ && listMultiSelection", "group": "8_gitlens_actions_", "order": 1}]}}, "gitlens.views.workspaces.addRepos": {"label": "Add Repositories...", "icon": "$(add)", "menus": {"view/item/context": [{"when": "viewItem =~ /gitlens:workspace\\b(?=.*?\\b\\+cloud\\b)/", "group": "inline", "order": 1}, {"when": "viewItem =~ /gitlens:workspace\\b(?=.*?\\b\\+cloud\\b)/ && !listMultiSelection", "group": "1_gitlens_actions", "order": 1}]}}, "gitlens.views.workspaces.addReposFromLinked": {"label": "Add Repositories from Linked Workspace...", "menus": {"view/item/context": [{"when": "viewItem =~ /gitlens:repositories\\b(?=.*?\\b\\+linked\\b)(?=.*?\\b\\+current\\b)/ && !listMultiSelection", "group": "1_gitlens_actions", "order": 3}]}}, "gitlens.views.workspaces.changeAutoAddSetting": {"label": "Change Linked Workspace Auto-Add Behavior...", "menus": {"view/item/context": [{"when": "viewItem =~ /(gitlens:workspace\\b(?=.*?\\b\\+(cloud|local)\\b)(?=.*?\\b\\+current\\b)(?=.*?\\b\\+hasPath\\b)|gitlens:repositories\\b(?=.*?\\b\\+linked\\b))/ && !listMultiSelection", "group": "2_gitlens_quickopen", "order": 6}]}}, "gitlens.views.workspaces.convert": {"label": "Convert to Cloud Workspace...", "icon": "$(cloud-upload)", "menus": {"view/item/context": [{"when": "viewItem =~ /gitlens:repositories\\b(?=.*?\\b\\+workspaces\\b)/ && gitlens:plus", "group": "inline", "order": 1}, {"when": "viewItem =~ /gitlens:repositories\\b(?=.*?\\b\\+workspaces\\b)/ && gitlens:plus", "group": "1_gitlens_actions", "order": 1}]}}, "gitlens.views.workspaces.copy": {"label": "Copy", "keybindings": [{"key": "ctrl+c", "when": "focusedView == gitlens.views.workspaces", "mac": "cmd+c"}]}, "gitlens.views.workspaces.create": {"label": "Create Cloud Workspace...", "icon": "$(add)", "commandPalette": "gitlens:plus", "menus": {"view/title": [{"when": "view == gitlens.views.workspaces && gitlens:plus", "group": "navigation", "order": 1}]}}, "gitlens.views.workspaces.createLocal": {"label": "Create VS Code Workspace...", "icon": "$(empty-window)", "menus": {"view/item/context": [{"when": "viewItem =~ /gitlens:workspace\\b(?=.*?\\b\\+(cloud|local)\\b)(?!.*?\\b\\+current\\b)(?!.*?\\b\\+hasPath\\b)(?!.*?\\b\\+empty\\b)/", "group": "inline", "order": 3}, {"when": "viewItem =~ /gitlens:workspace\\b(?=.*?\\b\\+(cloud|local)\\b)(?!.*?\\b\\+empty\\b)/ && !listMultiSelection", "group": "2_gitlens_quickopen", "order": 3}]}}, "gitlens.views.workspaces.delete": {"label": "Delete Workspace...", "icon": "$(trash)", "menus": {"view/item/context": [{"when": "viewItem =~ /gitlens:workspace\\b(?=.*?\\b\\+cloud\\b)/ && !listMultiSelection", "group": "6_gitlens_actions", "order": 1}]}}, "gitlens.views.workspaces.info": {"label": "Learn about Cloud Workspaces...", "icon": "$(info)", "menus": {"view/title": [{"when": "view == gitlens.views.workspaces", "group": "8_info", "order": 1}]}}, "gitlens.views.workspaces.locateAllRepos": {"label": "Locate Repositories...", "icon": "$(location)", "menus": {"view/item/context": [{"when": "viewItem =~ /gitlens:workspace\\b(?=.*?\\b\\+cloud\\b)(?!.*?\\b\\+empty\\b)/", "group": "inline", "order": 2}, {"when": "viewItem =~ /gitlens:workspace\\b(?=.*?\\b\\+cloud\\b)(?!.*?\\b\\+empty\\b)/ && !listMultiSelection", "group": "1_gitlens_actions", "order": 2}]}}, "gitlens.views.workspaces.openLocal": {"label": "Open VS Code Workspace in Current Window...", "icon": "$(window)", "menus": {"view/item/context": [{"when": "viewItem =~ /gitlens:workspace\\b(?=.*?\\b\\+(cloud|local)\\b)(?!.*?\\b\\+current\\b)(?=.*?\\b\\+hasPath\\b)/ && !listMultiSelection", "group": "2_gitlens_quickopen", "order": 4}]}}, "gitlens.views.workspaces.openLocalNewWindow": {"label": "Open VS Code Workspace in New Window...", "icon": "$(window)", "menus": {"view/item/context": [{"when": "viewItem =~ /gitlens:workspace\\b(?=.*?\\b\\+(cloud|local)\\b)(?!.*?\\b\\+current\\b)(?=.*?\\b\\+hasPath\\b)/", "group": "inline", "order": 3, "alt": "gitlens.views.workspaces.openLocal"}, {"when": "viewItem =~ /gitlens:workspace\\b(?=.*?\\b\\+(cloud|local)\\b)(?!.*?\\b\\+current\\b)(?=.*?\\b\\+hasPath\\b)/ && !listMultiSelection", "group": "2_gitlens_quickopen", "order": 5}]}}, "gitlens.views.workspaces.refresh": {"label": "Refresh", "icon": "$(refresh)", "menus": {"view/title": [{"when": "view == gitlens.views.workspaces", "group": "navigation", "order": 99}]}}, "gitlens.views.workspaces.repo.addToWindow": {"label": "Add Repository to VS Code Workspace", "menus": {"view/item/context": [{"when": "viewItem =~ /gitlens:repository\\b(?=.*?\\b\\+workspace\\b)/ && !listMultiSelection", "group": "0_1gitlens_actions", "order": 3}]}}, "gitlens.views.workspaces.repo.locate": {"label": "Locate Repository...", "icon": "$(location)", "menus": {"view/item/context": [{"when": "viewItem =~ /gitlens:repository\\b(?=.*?\\b\\+workspace\\b)(?!.*?\\b\\+local\\b)/ && !listMultiSelection", "group": "0_2<PERSON>lens_actions", "order": 2}, {"when": "viewItem =~ /gitlens:workspaceMissingRepository\\b/", "group": "inline", "order": 1}, {"when": "viewItem =~ /gitlens:workspaceMissingRepository\\b/ && !listMultiSelection", "group": "1_gitlens_actions", "order": 1}]}}, "gitlens.views.workspaces.repo.open": {"label": "Open Repository", "icon": "$(window)", "menus": {"view/item/context": [{"when": "viewItem =~ /gitlens:repository\\b(?=.*?\\b\\+workspace\\b)/ && !listMultiSelection", "group": "0_1gitlens_actions", "order": 1}]}}, "gitlens.views.workspaces.repo.openInNewWindow": {"label": "Open Repository in New Window", "icon": "$(empty-window)", "menus": {"view/item/context": [{"when": "viewItem =~ /gitlens:repository\\b(?=.*?\\b\\+workspace\\b)/", "group": "inline", "order": 100, "alt": "gitlens.views.workspaces.repo.open"}, {"when": "viewItem =~ /gitlens:repository\\b(?=.*?\\b\\+workspace\\b)/ && !listMultiSelection", "group": "0_1gitlens_actions", "order": 2}]}}, "gitlens.views.workspaces.repo.remove": {"label": "Remove from Workspace...", "icon": "$(trash)", "menus": {"view/item/context": [{"when": "viewItem =~ /gitlens:repository\\b(?=.*?\\b\\+workspace\\b)(?!.*?\\b\\+local\\b)/ && !listMultiSelection", "group": "0_3<PERSON><PERSON>s_actions", "order": 1}, {"when": "viewItem =~ /gitlens:workspaceMissingRepository\\b/ && !listMultiSelection", "group": "6_gitlens_actions", "order": 1}]}}, "gitlens.views.worktrees.attach": {"label": "Group into GitLens View", "icon": "$(close)", "menus": {"view/title": [{"when": "view == gitlens.views.worktrees", "group": "navigation", "order": 99}, {"when": "view == gitlens.views.worktrees", "group": "9_git<PERSON>s", "order": 0}]}}, "gitlens.views.worktrees.copy": {"label": "Copy", "keybindings": [{"key": "ctrl+c", "when": "focusedView == gitlens.views.worktrees", "mac": "cmd+c"}]}, "gitlens.views.worktrees.refresh": {"label": "Refresh", "icon": "$(refresh)", "menus": {"view/title": [{"when": "view == gitlens.views.worktrees", "group": "navigation", "order": 98}]}}, "gitlens.views.worktrees.setFilesLayoutToAuto": {"label": "View Files as Auto", "icon": "$(list-tree)", "menus": {"gitlens/views/grouped/more": [{"when": "gitlens:views:scm:grouped:view == worktrees && view == gitlens.views.scm.grouped && config.gitlens.views.worktrees.files.layout == tree", "group": "3_git<PERSON>s", "order": 1}], "view/title": [{"when": "view == gitlens.views.scm.grouped && gitlens:views:scm:grouped:view == worktrees && config.gitlens.views.worktrees.files.layout == tree", "group": "3_git<PERSON>s", "order": 1}, {"when": "view == gitlens.views.worktrees && config.gitlens.views.worktrees.files.layout == tree", "group": "3_git<PERSON>s", "order": 1}]}}, "gitlens.views.worktrees.setFilesLayoutToList": {"label": "View Files as List", "icon": "$(gitlens-list-auto)", "menus": {"gitlens/views/grouped/more": [{"when": "gitlens:views:scm:grouped:view == worktrees && view == gitlens.views.scm.grouped && config.gitlens.views.worktrees.files.layout == auto", "group": "3_git<PERSON>s", "order": 1}], "view/title": [{"when": "view == gitlens.views.scm.grouped && gitlens:views:scm:grouped:view == worktrees && config.gitlens.views.worktrees.files.layout == auto", "group": "3_git<PERSON>s", "order": 1}, {"when": "view == gitlens.views.worktrees && config.gitlens.views.worktrees.files.layout == auto", "group": "3_git<PERSON>s", "order": 1}]}}, "gitlens.views.worktrees.setFilesLayoutToTree": {"label": "View Files as Tree", "icon": "$(list-flat)", "menus": {"gitlens/views/grouped/more": [{"when": "gitlens:views:scm:grouped:view == worktrees && view == gitlens.views.scm.grouped && config.gitlens.views.worktrees.files.layout == list", "group": "3_git<PERSON>s", "order": 1}], "view/title": [{"when": "view == gitlens.views.scm.grouped && gitlens:views:scm:grouped:view == worktrees && config.gitlens.views.worktrees.files.layout == list", "group": "3_git<PERSON>s", "order": 1}, {"when": "view == gitlens.views.worktrees && config.gitlens.views.worktrees.files.layout == list", "group": "3_git<PERSON>s", "order": 1}]}}, "gitlens.views.worktrees.setLayoutToList": {"label": "View as List", "icon": "$(list-tree)", "menus": {"gitlens/views/grouped/more": [{"when": "gitlens:views:scm:grouped:view == worktrees && view == gitlens.views.scm.grouped && config.gitlens.views.worktrees.branches.layout == tree", "group": "3_git<PERSON>s", "order": 0}], "view/title": [{"when": "view == gitlens.views.scm.grouped && gitlens:views:scm:grouped:view == worktrees && config.gitlens.views.worktrees.branches.layout == tree", "group": "3_git<PERSON>s", "order": 0}, {"when": "view == gitlens.views.worktrees && config.gitlens.views.worktrees.branches.layout == tree", "group": "navigation", "order": 50}]}}, "gitlens.views.worktrees.setLayoutToTree": {"label": "View as Tree", "icon": "$(list-flat)", "menus": {"gitlens/views/grouped/more": [{"when": "gitlens:views:scm:grouped:view == worktrees && view == gitlens.views.scm.grouped && config.gitlens.views.worktrees.branches.layout == list", "group": "3_git<PERSON>s", "order": 0}], "view/title": [{"when": "view == gitlens.views.scm.grouped && gitlens:views:scm:grouped:view == worktrees && config.gitlens.views.worktrees.branches.layout == list", "group": "3_git<PERSON>s", "order": 0}, {"when": "view == gitlens.views.worktrees && config.gitlens.views.worktrees.branches.layout == list", "group": "navigation", "order": 50}]}}, "gitlens.views.worktrees.setShowAvatarsOff": {"label": "Hide Avatars", "menus": {"gitlens/views/grouped/more": [{"when": "gitlens:views:scm:grouped:view == worktrees && view == gitlens.views.scm.grouped && config.gitlens.views.worktrees.avatars", "group": "5_gitlens", "order": 1}], "view/title": [{"when": "view == gitlens.views.scm.grouped && gitlens:views:scm:grouped:view == worktrees && config.gitlens.views.worktrees.avatars", "group": "5_gitlens", "order": 1}, {"when": "view == gitlens.views.worktrees && config.gitlens.views.worktrees.avatars", "group": "5_gitlens", "order": 1}]}}, "gitlens.views.worktrees.setShowAvatarsOn": {"label": "Show Avatars", "menus": {"gitlens/views/grouped/more": [{"when": "gitlens:views:scm:grouped:view == worktrees && view == gitlens.views.scm.grouped && !config.gitlens.views.worktrees.avatars", "group": "5_gitlens", "order": 1}], "view/title": [{"when": "view == gitlens.views.scm.grouped && gitlens:views:scm:grouped:view == worktrees && !config.gitlens.views.worktrees.avatars", "group": "5_gitlens", "order": 1}, {"when": "view == gitlens.views.worktrees && !config.gitlens.views.worktrees.avatars", "group": "5_gitlens", "order": 1}]}}, "gitlens.views.worktrees.setShowBranchComparisonOff": {"label": "Hide Branch Comparison", "menus": {"gitlens/views/grouped/more": [{"when": "gitlens:views:scm:grouped:view == worktrees && view == gitlens.views.scm.grouped && config.gitlens.views.worktrees.showBranchComparison", "group": "5_gitlens", "order": 2}], "view/title": [{"when": "view == gitlens.views.scm.grouped && gitlens:views:scm:grouped:view == worktrees && config.gitlens.views.worktrees.showBranchComparison", "group": "5_gitlens", "order": 2}, {"when": "view == gitlens.views.worktrees && config.gitlens.views.worktrees.showBranchComparison", "group": "5_gitlens", "order": 2}]}}, "gitlens.views.worktrees.setShowBranchComparisonOn": {"label": "Show Branch Comparison", "menus": {"gitlens/views/grouped/more": [{"when": "gitlens:views:scm:grouped:view == worktrees && view == gitlens.views.scm.grouped && !config.gitlens.views.worktrees.showBranchComparison", "group": "5_gitlens", "order": 2}], "view/title": [{"when": "view == gitlens.views.scm.grouped && gitlens:views:scm:grouped:view == worktrees && !config.gitlens.views.worktrees.showBranchComparison", "group": "5_gitlens", "order": 2}, {"when": "view == gitlens.views.worktrees && !config.gitlens.views.worktrees.showBranchComparison", "group": "5_gitlens", "order": 2}]}}, "gitlens.views.worktrees.setShowBranchPullRequestOff": {"label": "Hide Branch Pull Requests", "menus": {"gitlens/views/grouped/more": [{"when": "gitlens:views:scm:grouped:view == worktrees && view == gitlens.views.scm.grouped && config.gitlens.views.worktrees.pullRequests.enabled && config.gitlens.views.worktrees.pullRequests.showForBranches", "group": "5_gitlens", "order": 3}], "view/title": [{"when": "view == gitlens.views.scm.grouped && gitlens:views:scm:grouped:view == worktrees && config.gitlens.views.worktrees.pullRequests.enabled && config.gitlens.views.worktrees.pullRequests.showForBranches", "group": "5_gitlens", "order": 3}, {"when": "view == gitlens.views.worktrees && config.gitlens.views.worktrees.pullRequests.enabled && config.gitlens.views.worktrees.pullRequests.showForBranches", "group": "5_gitlens", "order": 3}]}}, "gitlens.views.worktrees.setShowBranchPullRequestOn": {"label": "Show Branch Pull Requests", "menus": {"gitlens/views/grouped/more": [{"when": "gitlens:views:scm:grouped:view == worktrees && view == gitlens.views.scm.grouped && !config.gitlens.views.worktrees.pullRequests.enabled && !config.gitlens.views.worktrees.pullRequests.showForBranches", "group": "5_gitlens", "order": 3}], "view/title": [{"when": "view == gitlens.views.scm.grouped && gitlens:views:scm:grouped:view == worktrees && !config.gitlens.views.worktrees.pullRequests.enabled && !config.gitlens.views.worktrees.pullRequests.showForBranches", "group": "5_gitlens", "order": 3}, {"when": "view == gitlens.views.worktrees && !config.gitlens.views.worktrees.pullRequests.enabled && !config.gitlens.views.worktrees.pullRequests.showForBranches", "group": "5_gitlens", "order": 3}]}}, "gitlens.views.worktrees.setShowStashesOff": {"label": "<PERSON><PERSON> St<PERSON>", "menus": {"gitlens/views/grouped/more": [{"when": "gitlens:views:scm:grouped:view == worktrees && view == gitlens.views.scm.grouped && !gitlens:hasVirtualFolders && config.gitlens.views.worktrees.showStashes", "group": "5_gitlens", "order": 5}], "view/title": [{"when": "view == gitlens.views.scm.grouped && gitlens:views:scm:grouped:view == worktrees && !gitlens:hasVirtualFolders && config.gitlens.views.worktrees.showStashes", "group": "5_gitlens", "order": 5}, {"when": "view == gitlens.views.worktrees && !gitlens:hasVirtualFolders && config.gitlens.views.worktrees.showStashes", "group": "5_gitlens", "order": 5}]}}, "gitlens.views.worktrees.setShowStashesOn": {"label": "Show Stashes", "menus": {"gitlens/views/grouped/more": [{"when": "gitlens:views:scm:grouped:view == worktrees && view == gitlens.views.scm.grouped && !gitlens:hasVirtualFolders && !config.gitlens.views.worktrees.showStashes", "group": "5_gitlens", "order": 5}], "view/title": [{"when": "view == gitlens.views.scm.grouped && gitlens:views:scm:grouped:view == worktrees && !gitlens:hasVirtualFolders && !config.gitlens.views.worktrees.showStashes", "group": "5_gitlens", "order": 5}, {"when": "view == gitlens.views.worktrees && !gitlens:hasVirtualFolders && !config.gitlens.views.worktrees.showStashes", "group": "5_gitlens", "order": 5}]}}, "gitlens.views.worktrees.viewOptionsTitle": {"label": "Worktrees View Options", "enablement": "false", "menus": {"view/title": [{"when": "view == gitlens.views.scm.grouped && gitlens:views:scm:grouped:view == worktrees", "group": "1_gitlens_1", "order": 1}]}}, "gitlens.visualizeHistory.file": {"label": "Open Visual File History", "icon": "$(graph-scatter)", "commandPalette": "gitlens:enabled && resource in gitlens:tabs:tracked"}, "gitlens.visualizeHistory.file:editor": {"label": "Open Visual File History", "icon": "$(graph-scatter)", "menus": {"gitlens/editor/history": [{"group": "1_gitlens", "order": 2}]}}, "gitlens.visualizeHistory.file:explorer": {"label": "Open Visual File History", "icon": "$(graph-scatter)", "menus": {"gitlens/explorer/file/history": [{"group": "1_gitlens", "order": 2}]}}, "gitlens.visualizeHistory.file:scm": {"label": "Open Visual File History", "icon": "$(graph-scatter)", "menus": {"gitlens/scm/resourceState/history": [{"group": "1_gitlens", "order": 2}]}}, "gitlens.visualizeHistory.file:views": {"label": "Open Visual File History", "icon": "$(graph-scatter)", "menus": {"gitlens/commit/file/history": [{"group": "1_gitlens", "order": 2}]}}, "gitlens.visualizeHistory.folder:explorer": {"label": "Open Visual Folder History", "icon": "$(graph-scatter)", "menus": {"gitlens/explorer/folder/history": [{"group": "1_gitlens", "order": 2}]}}, "gitlens.visualizeHistory.folder:scm": {"label": "Open Visual Folder History", "icon": "$(graph-scatter)", "menus": {"gitlens/scm/resourceFolder/history": [{"group": "1_gitlens", "order": 2}]}}}, "submenus": {"gitlens/commit/browse": {"label": "Browse", "menus": {"view/item/context": [{"when": "viewItem =~ /gitlens:(branch|commit|file\\b(?=.*?\\b\\+committed\\b)|stash|tag)\\b/ && !listMultiSelection && !gitlens:hasVirtualFolders", "group": "3_gitlens_explore", "order": 100}]}}, "gitlens/commit/changes": {"label": "Open Changes", "menus": {"view/item/context": [{"when": "viewItem =~ /gitlens:(compare:results(?!:)\\b(?!.*?\\b\\+filtered\\b)|commit|stash|results:files|status-branch:files|status:upstream:(ahead|behind))\\b/ && !listMultiSelection", "group": "2_gitlens_quickopen", "order": 1}]}}, "gitlens/commit/copy": {"label": "<PERSON><PERSON> As", "menus": {"view/item/context": [{"when": "viewItem =~ /gitlens:(branch|commit|remote|repo-folder|repository|stash|tag|file\\b(?=.*?\\b\\+committed\\b))\\b/ && !listMultiSelection", "group": "7_gitlens_cutcopypaste", "order": 10}]}}, "gitlens/commit/file/changes": {"label": "Open Changes with", "menus": {"view/item/context": [{"when": "viewItem =~ /gitlens:file\\b/ && !listMultiSelection", "group": "2_gitlens_quickopen", "order": 2}]}}, "gitlens/commit/file/commit": {"label": "Commit", "menus": {"view/item/context": [{"when": "viewItem =~ /gitlens:file\\b(?=.*?\\b\\+committed\\b)/ && view =~ /^gitlens\\.views\\.(fileHistory|lineHistory)/ && !listMultiSelection", "group": "3_gitlens_explore", "order": 1}]}}, "gitlens/commit/file/history": {"label": "File History", "menus": {"view/item/context": [{"when": "viewItem =~ /gitlens:file\\b/ && !listMultiSelection", "group": "3_gitlens_explore", "order": 2}]}}, "gitlens/comparison/results/files/filter": {"label": "Filter Files", "icon": "$(filter)", "menus": {"view/item/context": [{"when": "viewItem =~ /gitlens:results:files\\b(?=.*?\\b\\+filterable\\b)(?!.*?\\b\\+filtered\\b)/ && !listMultiSelection", "group": "1_gitlens", "order": 2}]}}, "gitlens/comparison/results/files/filter/inline": {"label": "Filter Files", "icon": "$(filter)", "menus": {"view/item/context": [{"when": "viewItem =~ /gitlens:results:files\\b(?=.*?\\b\\+filterable\\b)(?!.*?\\b\\+filtered\\b)/", "group": "inline", "order": 99}]}}, "gitlens/comparison/results/files/filtered": {"label": "Filter Files", "icon": "$(filter-filled)", "menus": {"view/item/context": [{"when": "viewItem =~ /gitlens:results:files\\b(?=.*?\\b\\+filterable\\b)(?=.*?\\b\\+filtered\\b)/ && !listMultiSelection", "group": "1_gitlens", "order": 2}]}}, "gitlens/comparison/results/files/filtered/inline": {"label": "Filter Files", "icon": "$(filter-filled)", "menus": {"view/item/context": [{"when": "viewItem =~ /gitlens:results:files\\b(?=.*?\\b\\+filterable\\b)(?=.*?\\b\\+filtered\\b)/", "group": "inline", "order": 99}]}}, "gitlens/editor/annotations": {"label": "File Annotations", "icon": "$(gitlens-gitlens)", "menus": {"editor/context": [{"when": "resourceScheme in gitlens:schemes:trackable && resource in gitlens:tabs:blameable && editorTextFocus && config.gitlens.menus.editor.blame", "group": "1_z_gitlens_open_file", "order": 1}], "editor/title": [{"when": "resource in gitlens:tabs:blameable && resource not in gitlens:tabs:annotated && !gitlens:window:annotated && config.gitlens.menus.editorGroup.blame && !config.gitlens.fileAnnotations.command", "group": "navigation", "order": 100}]}}, "gitlens/editor/changes": {"label": "Open Changes", "menus": {"editor/title/context": [{"when": "resourceScheme in gitlens:schemes:trackable && gitlens:enabled && config.gitlens.menus.editorTab.compare", "group": "2_a_gitlens_open", "order": 1}]}}, "gitlens/editor/context/changes": {"label": "Open Changes", "menus": {"editor/context": [{"when": "resourceScheme in gitlens:schemes:trackable && editorTextFocus && config.gitlens.menus.editor.compare", "group": "1_z_gitlens_open", "order": 1}]}}, "gitlens/editor/context/openOn": {"label": "Open on Remote (Web)", "menus": {"editor/context": [{"when": "resourceScheme in gitlens:schemes:trackable && editorTextFocus && gitlens:repos:withRemotes && config.gitlens.menus.editor.remote", "group": "1_z_gitlens_open", "order": 2}]}}, "gitlens/editor/history": {"label": "File History", "menus": {"editor/context": [{"when": "resourceScheme in gitlens:schemes:trackable && resource in gitlens:tabs:tracked && config.gitlens.menus.editor.history", "group": "1_z_gitlens_open_file", "order": 2}], "editor/title/context": [{"when": "resourceScheme in gitlens:schemes:trackable && gitlens:enabled && config.gitlens.menus.editorTab.history", "group": "2_a_gitlens_open_file", "order": 1}]}}, "gitlens/editor/lineNumber/context/changes": {"label": "Open Changes", "menus": {"editor/lineNumber/context": [{"when": "resourceScheme in gitlens:schemes:trackable && config.gitlens.menus.editorGutter.compare", "group": "3_git<PERSON>s", "order": 1}]}}, "gitlens/editor/lineNumber/context/openOn": {"label": "Open on Remote (Web)", "menus": {"editor/lineNumber/context": [{"when": "resourceScheme in gitlens:schemes:trackable && gitlens:repos:withRemotes && config.gitlens.menus.editorGutter.remote", "group": "3_git<PERSON>s", "order": 2}]}}, "gitlens/editor/lineNumber/context/share": {"label": "Share", "menus": {"editor/lineNumber/context": [{"when": "resourceScheme in gitlens:schemes:trackable && gitlens:repos:withRemotes && config.gitlens.menus.editorGutter.share", "group": "2_git<PERSON>s", "order": 2}]}}, "gitlens/editor/openOn": {"label": "Open on Remote (Web)", "menus": {"editor/title/context": [{"when": "resourceScheme in gitlens:schemes:trackable && gitlens:enabled && gitlens:repos:withRemotes && config.gitlens.menus.editorTab.remote", "group": "2_a_gitlens_open", "order": 2}]}}, "gitlens/explorer/changes": {"label": "Open Changes", "menus": {"explorer/context": [{"when": "!explorerResourceIsRoot && gitlens:enabled && config.gitlens.menus.explorer.compare", "group": "4_t_gitlens", "order": 0}]}}, "gitlens/explorer/file/history": {"label": "File History", "menus": {"explorer/context": [{"when": "!explorerResourceIsRoot && !explorerResourceIsFolder && gitlens:enabled && config.gitlens.menus.explorer.history && !listMultiSelection", "group": "4_timeline", "order": 0}]}}, "gitlens/explorer/folder/history": {"label": "Folder History", "menus": {"explorer/context": [{"when": "(explorerResourceIsRoot || explorerResourceIsFolder) && gitlens:enabled && !listMultiSelection && config.gitlens.menus.explorer.history", "group": "4_timeline", "order": 0}], "view/item/context": [{"when": "viewItem =~ /gitlens:folder\\b/ && !listMultiSelection", "group": "4_timeline", "order": 1}]}}, "gitlens/explorer/openOn": {"label": "Open on Remote (Web)", "menus": {"explorer/context": [{"when": "!explorerResourceIsRoot && gitlens:enabled && gitlens:repos:withRemotes && config.gitlens.menus.explorer.remote", "group": "4_t_gitlens", "order": 1}]}}, "gitlens/graph/commit/changes": {"label": "Open Changes", "menus": {"webview/context": [{"when": "webviewItem =~ /gitlens:(commit|stash|wip)\\b/ && !listMultiSelection", "group": "2_gitlens_quickopen", "order": 1}]}}, "gitlens/graph/configuration": {"label": "Commit Graph Settings", "icon": "$(gear)", "menus": {"editor/title": [{"when": "activeWebviewPanelId === gitlens.graph", "group": "navigation", "order": -98}], "view/title": [{"when": "view == gitlens.views.graph", "group": "navigation", "order": -98}]}}, "gitlens/graph/markers": {"label": "Scroll Markers", "menus": {"webview/context": [{"when": "webviewItem =~ /gitlens:graph:(columns|settings)\\b/", "group": "0_markers", "order": 0}]}}, "gitlens/scm/resourceFolder/changes": {"label": "Open Changes with", "menus": {"scm/resourceFolder/context": [{"when": "scmResourceGroup =~ /^(workingTree|index|merge)$/ && scmProvider == git && gitlens:enabled && config.gitlens.menus.scmItem.compare", "group": "2_git<PERSON>s", "order": 1}]}}, "gitlens/scm/resourceFolder/history": {"label": "Folder History", "menus": {"scm/resourceFolder/context": [{"when": "scmResourceGroup =~ /^(workingTree|index|merge)$/ && scmProvider == git && gitlens:enabled && config.gitlens.menus.scmItem.history", "group": "2_gitlens_1", "order": 1}]}}, "gitlens/scm/resourceGroup/changes": {"label": "Open Changes", "menus": {"scm/resourceGroup/context": [{"when": "scmResourceGroup =~ /^(workingTree|index|merge)$/ && scmProvider == git && gitlens:enabled && config.gitlens.menus.scmGroup.compare", "group": "2_git<PERSON>s", "order": 1}]}}, "gitlens/scm/resourceState/changes": {"label": "Open Changes with", "menus": {"scm/resourceState/context": [{"when": "scmResourceGroup =~ /^(workingTree|index|merge)$/ && scmProvider == git && gitlens:enabled && config.gitlens.menus.scmItem.compare", "group": "navigation"}]}}, "gitlens/scm/resourceState/history": {"label": "File History", "menus": {"scm/resourceState/context": [{"when": "scmResourceGroup =~ /^(workingTree|index|merge)$/ && scmProvider == git && gitlens:enabled && config.gitlens.menus.scmItem.history", "group": "1_a_gitlens", "order": 2}]}}, "gitlens/scm/resourceState/openOn": {"label": "Open on Remote (Web)", "menus": {"scm/resourceState/context": [{"when": "scmResourceGroup =~ /^(workingTree|index|merge)$/ && scmProvider == git && gitlens:enabled && gitlens:repos:withRemotes && config.gitlens.menus.scmItem.remote", "group": "navigation"}]}}, "gitlens/share": {"label": "Share", "menus": {"scm/resourceState/context": [{"when": "scmResourceGroup =~ /^(workingTree|index|merge)$/ && scmProvider == git && gitlens:enabled && config.gitlens.menus.scmItem.share", "group": "7_a_gitlens_share", "order": 1}], "view/item/context": [{"when": "viewItem =~ /gitlens:(branch|commit|compare:(branch(?=.*?\\b\\+comparing\\b)|results(:commits(?!:)|(?!:)))|remote|repo-folder|repository|stash|status:upstream|tag|workspace|file\\b(?=.*?\\b\\+committed\\b))\\b/", "group": "7_gitlens_a_share", "order": 1}], "webview/context": [{"when": "webviewItem =~ /gitlens:(branch|commit|stash|tag)\\b/", "group": "7_gitlens_a_share", "order": 1}]}}, "gitlens/view/repositories/sections": {"label": "Sections", "menus": {"gitlens/views/grouped/more": [{"when": "gitlens:views:scm:grouped:view == repositories && view == gitlens.views.scm.grouped", "group": "4_git<PERSON>s", "order": 1}], "view/title": [{"when": "(view == gitlens.views.repositories) || (view == gitlens.views.scm.grouped && gitlens:views:scm:grouped:view == repositories)", "group": "4_git<PERSON>s", "order": 1}]}}, "gitlens/view/searchAndCompare/new": {"label": "New Search or Compare", "icon": "$(add)", "menus": {"view/title": [{"when": "view == gitlens.views.searchAndCompare", "group": "navigation", "order": 10}]}}, "gitlens/views/grouped/attachOrDetach": {"label": "Group / Detach Views", "menus": {"view/title": [{"when": "view == gitlens.views.scm.grouped && !gitlens:views:scm:grouped:welcome", "group": "1_gitlens", "order": 1}]}}, "gitlens/views/grouped/branches": {"label": "Branches (3)", "icon": "$(gitlens-branches-view-filled)", "menus": {"view/title": [{"when": "view == gitlens.views.scm.grouped && gitlens:views:scm:grouped:view == branches && !gitlens:views:scm:grouped:welcome && config.gitlens.views.scm.grouped.views.branches", "group": "navigation", "order": 3}]}}, "gitlens/views/grouped/commits": {"label": "Commits (1)", "icon": "$(gitlens-commits-view-filled)", "menus": {"view/title": [{"when": "view == gitlens.views.scm.grouped && gitlens:views:scm:grouped:view == commits && !gitlens:views:scm:grouped:welcome && config.gitlens.views.scm.grouped.views.commits", "group": "navigation", "order": 1}]}}, "gitlens/views/grouped/contributors": {"label": "Contributors (7)", "icon": "$(gitlens-contributors-view-filled)", "menus": {"view/title": [{"when": "view == gitlens.views.scm.grouped && gitlens:views:scm:grouped:view == contributors && !gitlens:views:scm:grouped:welcome && config.gitlens.views.scm.grouped.views.contributors", "group": "navigation", "order": 7}]}}, "gitlens/views/grouped/fileHistory": {"label": "File History (8)", "icon": "$(gitlens-history-view-filled)", "menus": {"view/title": [{"when": "view == gitlens.views.scm.grouped && gitlens:views:scm:grouped:view == fileHistory && !gitlens:views:scm:grouped:welcome && config.gitlens.views.scm.grouped.views.fileHistory", "group": "navigation", "order": 9}]}}, "gitlens/views/grouped/launchpad": {"label": "Launchpad (9)", "icon": "$(gitlens-launchpad-view-filled)", "menus": {"view/title": [{"when": "view == gitlens.views.scm.grouped && gitlens:views:scm:grouped:view == launchpad && !gitlens:views:scm:grouped:welcome && config.gitlens.views.scm.grouped.views.launchpad", "group": "navigation", "order": 10}]}}, "gitlens/views/grouped/more": {"label": "View Options", "icon": "$(ellipsis)", "menus": {"gitlens/views/grouped/branches": [{"when": "gitlens:views:scm:grouped:view == branches", "group": "9_git<PERSON>s", "order": 1}], "gitlens/views/grouped/commits": [{"when": "gitlens:views:scm:grouped:view == commits", "group": "9_git<PERSON>s", "order": 1}], "gitlens/views/grouped/contributors": [{"when": "gitlens:views:scm:grouped:view == contributors", "group": "9_git<PERSON>s", "order": 1}], "gitlens/views/grouped/fileHistory": [{"when": "gitlens:views:scm:grouped:view == fileHistory", "group": "9_git<PERSON>s", "order": 1}], "gitlens/views/grouped/launchpad": [{"when": "gitlens:views:scm:grouped:view == launchpad", "group": "9_git<PERSON>s", "order": 1}], "gitlens/views/grouped/remotes": [{"when": "gitlens:views:scm:grouped:view == remotes", "group": "9_git<PERSON>s", "order": 1}], "gitlens/views/grouped/repositories": [{"when": "gitlens:views:scm:grouped:view == repositories", "group": "9_git<PERSON>s", "order": 1}], "gitlens/views/grouped/searchAndCompare": [{"when": "gitlens:views:scm:grouped:view == searchAndCompare", "group": "9_git<PERSON>s", "order": 1}], "gitlens/views/grouped/stashes": [{"when": "gitlens:views:scm:grouped:view == stashes", "group": "9_git<PERSON>s", "order": 1}], "gitlens/views/grouped/tags": [{"when": "gitlens:views:scm:grouped:view == tags", "group": "9_git<PERSON>s", "order": 1}], "gitlens/views/grouped/worktrees": [{"when": "gitlens:views:scm:grouped:view == worktrees", "group": "9_git<PERSON>s", "order": 1}]}}, "gitlens/views/grouped/remotes": {"label": "Remotes (4)", "icon": "$(gitlens-remotes-view-filled)", "menus": {"view/title": [{"when": "view == gitlens.views.scm.grouped && gitlens:views:scm:grouped:view == remotes && !gitlens:views:scm:grouped:welcome && config.gitlens.views.scm.grouped.views.remotes", "group": "navigation", "order": 4}]}}, "gitlens/views/grouped/repositories": {"label": "Repositories", "icon": "$(gitlens-repositories-view-filled)", "menus": {"view/title": [{"when": "view == gitlens.views.scm.grouped && gitlens:views:scm:grouped:view == repositories && !gitlens:views:scm:grouped:welcome && config.gitlens.views.scm.grouped.views.repositories", "group": "navigation", "order": 8}]}}, "gitlens/views/grouped/searchAndCompare": {"label": "Search & Compare (0)", "icon": "$(gitlens-search-view-filled)", "menus": {"view/title": [{"when": "view == gitlens.views.scm.grouped && gitlens:views:scm:grouped:view == searchAndCompare && !gitlens:views:scm:grouped:welcome && config.gitlens.views.scm.grouped.views.searchAndCompare", "group": "navigation", "order": 11}]}}, "gitlens/views/grouped/stashes": {"label": "Stash<PERSON> (5)", "icon": "$(gitlens-stashes-view-filled)", "menus": {"view/title": [{"when": "view == gitlens.views.scm.grouped && gitlens:views:scm:grouped:view == stashes && !gitlens:hasVirtualFolders && !gitlens:views:scm:grouped:welcome && config.gitlens.views.scm.grouped.views.stashes", "group": "navigation", "order": 5}]}}, "gitlens/views/grouped/tags": {"label": "Tags (6)", "icon": "$(gitlens-tags-view-filled)", "menus": {"view/title": [{"when": "view == gitlens.views.scm.grouped && gitlens:views:scm:grouped:view == tags && !gitlens:views:scm:grouped:welcome && config.gitlens.views.scm.grouped.views.tags", "group": "navigation", "order": 6}]}}, "gitlens/views/grouped/visibility": {"label": "Show / Hide Views", "menus": {"view/title": [{"when": "view == gitlens.views.scm.grouped && !gitlens:views:scm:grouped:welcome", "group": "1_gitlens", "order": 2}]}}, "gitlens/views/grouped/worktrees": {"label": "Worktrees (2)", "icon": "$(gitlens-worktrees-view-filled)", "menus": {"view/title": [{"when": "view == gitlens.views.scm.grouped && gitlens:views:scm:grouped:view == worktrees && !gitlens:hasVirtualFolders && !gitlens:plus:disabled && !gitlens:views:scm:grouped:welcome && config.gitlens.views.scm.grouped.views.worktrees", "group": "navigation", "order": 2}]}}}, "keybindings": [{"command": "gitlens.key.alt+,", "key": "alt+,", "when": "gitlens:key:,"}, {"command": "gitlens.key.alt+.", "key": "alt+.", "when": "gitlens:key:."}, {"command": "gitlens.key.alt+enter", "key": "alt+enter", "when": "gitlens:key:alt+enter"}, {"command": "gitlens.key.alt+left", "key": "alt+left", "when": "gitlens:key:alt+left"}, {"command": "gitlens.key.alt+right", "key": "alt+right", "when": "gitlens:key:alt+right"}, {"command": "gitlens.key.ctrl+enter", "key": "ctrl+enter", "when": "gitlens:key:ctrl+enter"}, {"command": "gitlens.key.ctrl+left", "key": "ctrl+left", "when": "gitlens:key:ctrl+left", "mac": "cmd+left"}, {"command": "gitlens.key.ctrl+right", "key": "ctrl+right", "when": "gitlens:key:ctrl+right", "mac": "cmd+right"}, {"command": "workbench.view.scm", "key": "ctrl+shift+g g", "when": "config.gitlens.keymap == chorded && !gitlens:disabled", "mac": "ctrl+shift+g"}, {"command": "gitlens.key.escape", "key": "escape", "when": "gitlens:key:escape && editorTextFocus && !findWidgetVisible && !quickFixWidgetVisible && !renameInputVisible && !suggestWidgetVisible && !referenceSearchVisible && !codeActionMenuVisible && !parameterHintsVisible && !isInEmbeddedEditor"}, {"command": "gitlens.key.left", "key": "left", "when": "gitlens:key:left"}, {"command": "gitlens.key.right", "key": "right", "when": "gitlens:key:right"}], "views": {"gitlens.views.branches": {"name": "Branches", "when": "!gitlens:disabled && !(config.gitlens.views.scm.grouped.views.branches || gitlens:views:scm:grouped:views:branches)", "contextualTitle": "GitLens", "icon": "$(gitlens-branches-view)", "visibility": "collapsed", "container": "scm", "order": 2}, "gitlens.views.commitDetails": {"type": "webview", "name": "Inspect", "when": "!gitlens:disabled", "contextualTitle": "GitLens", "icon": "$(gitlens-commit-view)", "initialSize": 6, "visibility": "visible", "container": "gitlensInspect", "order": 0}, "gitlens.views.commits": {"name": "Commits", "when": "!gitlens:disabled && !(config.gitlens.views.scm.grouped.views.commits || gitlens:views:scm:grouped:views:commits)", "contextualTitle": "GitLens", "icon": "$(gitlens-commits-view)", "visibility": "visible", "container": "scm", "order": 1}, "gitlens.views.contributors": {"name": "Contributors", "when": "!gitlens:disabled && !(config.gitlens.views.scm.grouped.views.contributors || gitlens:views:scm:grouped:views:contributors)", "contextualTitle": "GitLens", "icon": "$(gitlens-contributors-view)", "visibility": "collapsed", "container": "scm", "order": 7}, "gitlens.views.drafts": {"name": "<PERSON>", "when": "!gitlens:untrusted && !gitlens:hasVirtualFolders && gitlens:gk:organization:drafts:enabled && config.gitlens.cloudPatches.enabled", "contextualTitle": "GitLens", "icon": "$(gitlens-cloud-patch)", "initialSize": 2, "visibility": "collapsed", "container": "gitlens", "order": 2, "welcomeContent": [{"contents": "Cloud Patches ᴘʀᴇᴠɪᴇᴡ — privately and securely share code with specific teammates and other developers, accessible from anywhere. Enhance collaboration without adding noise to your repositories."}, {"contents": "[Create Cloud Patch](command:gitlens.views.drafts.create)", "when": "gitlens:plus"}, {"contents": "[Try GitLens Pro](command:gitlens.plus.signUp?%7B%22source%22%3A%22cloud-patches%22%7D)\n\nGet 14 days of GitLens Pro for free — no credit card required. Or [sign in](command:gitlens.plus.login?%7B%22source%22%3A%22cloud-patches%22%7D).", "when": "!gitlens:plus"}, {"contents": "An account is required and may require [GitLens Pro](https://help.gitkraken.com/gitlens/gitlens-community-vs-gitlens-pro/) in the future.", "when": "gitlens:plus:state != 6"}]}, "gitlens.views.fileHistory": {"name": "File History", "when": "!gitlens:disabled && !(config.gitlens.views.scm.grouped.views.fileHistory || gitlens:views:scm:grouped:views:fileHistory)", "contextualTitle": "GitLens", "icon": "$(gitlens-history-view)", "initialSize": 2, "visibility": "visible", "container": "gitlensInspect", "order": 3}, "gitlens.views.graph": {"type": "webview", "name": "Graph", "when": "!gitlens:disabled && !gitlens:plus:disabled", "contextualTitle": "GitLens", "icon": "$(gitlens-graph)", "initialSize": 4, "visibility": "visible", "container": "gitlensPanel", "order": 0}, "gitlens.views.graphDetails": {"type": "webview", "name": "Graph Details", "when": "!gitlens:disabled && !gitlens:plus:disabled", "contextualTitle": "GitLens", "icon": "$(gitlens-commit-view)", "initialSize": 1, "visibility": "visible", "container": "gitlensPanel", "order": 1}, "gitlens.views.home": {"type": "webview", "name": "Home", "contextualTitle": "GitLens", "icon": "$(gitlens-gitlens)", "initialSize": 6, "visibility": "visible", "container": "gitlens", "order": 0}, "gitlens.views.launchpad": {"name": "Launchpad", "when": "!(config.gitlens.views.scm.grouped.views.launchpad || gitlens:views:scm:grouped:views:launchpad)", "contextualTitle": "GitLens", "icon": "$(gitlens-launchpad-view)", "initialSize": 2, "visibility": "collapsed", "container": "gitlens", "order": 1, "welcomeContent": [{"contents": "[Launchpad](command:gitlens.views.launchpad.info \"Learn about Launchpad\") — organizes your pull requests into actionable groups to help you focus and keep your team unblocked."}, {"contents": "[Connect an Integration...](command:gitlens.showLaunchpad?%7B%22source%22%3A%22launchpad-view%22%7D)\n\nAllows Launchpad to organize your pull requests into actionable groups and keep your team unblocked.", "when": "!gitlens:launchpad:connected"}, {"contents": "[Resend Verification Email](command:gitlens.plus.resendVerification?%7B%22source%22%3A%22launchpad-view%22%7D)\n\nYou must verify your email before you can continue or [recheck Status](command:gitlens.plus.validate?%7B%22source%22%3A%22launchpad-view%22%7D).", "when": "gitlens:launchpad:connected && gitlens:plus:state == -1"}, {"contents": "[Try GitLens Pro](command:gitlens.plus.signUp?%7B%22source%22%3A%22launchpad-view%22%7D)\n\nGet 14 days of GitLens Pro for free — no credit card required. Or [sign in](command:gitlens.plus.login?%7B%22source%22%3A%22launchpad-view%22%7D).", "when": "gitlens:launchpad:connected && gitlens:plus:state == 0"}, {"contents": "[Upgrade to Pro](command:gitlens.plus.upgrade?%7B%22source%22%3A%22launchpad-view%22%7D)", "when": "gitlens:launchpad:connected && gitlens:plus:state == 4"}, {"contents": "Limited-time sale on GitLens Pro.", "when": "gitlens:launchpad:connected && gitlens:plus:state == 4 && gitlens:promo && gitlens:promo != pro50"}, {"contents": "Save 50% on GitLens Pro.", "when": "gitlens:launchpad:connected && gitlens:plus:state == 4 && gitlens:promo == pro50"}, {"contents": "Your Pro trial has ended. Please upgrade for full access to Launchpad and other Pro features.", "when": "gitlens:launchpad:connected && gitlens:plus:state == 4"}, {"contents": "[Continue](command:gitlens.plus.reactivateProTrial?%7B%22source%22%3A%22launchpad-view%22%7D)\n\nReactivate your Pro trial and experience Launchpad and all the new Pro features — free for another 14 days!", "when": "gitlens:launchpad:connected && gitlens:plus:state == 5"}]}, "gitlens.views.lineHistory": {"name": "Line History", "when": "!gitlens:disabled && !gitlens:hasVirtualFolders", "contextualTitle": "GitLens", "icon": "$(gitlens-history-view)", "initialSize": 1, "visibility": "collapsed", "container": "gitlensInspect", "order": 2}, "gitlens.views.patchDetails": {"type": "webview", "name": "Patch", "when": "!gitlens:untrusted && config.gitlens.cloudPatches.enabled && gitlens:views:patchDetails:mode", "contextualTitle": "GitLens", "icon": "$(gitlens-cloud-patch)", "initialSize": 24, "container": "gitlensPatch", "order": 0}, "gitlens.views.pullRequest": {"name": "Pull Request", "when": "!gitlens:disabled && gitlens:views:pullRequest:visible", "contextualTitle": "GitLens", "icon": "$(git-pull-request)", "initialSize": 1, "visibility": "visible", "container": "gitlensInspect", "order": 1}, "gitlens.views.remotes": {"name": "Remotes", "when": "!gitlens:disabled && !(config.gitlens.views.scm.grouped.views.remotes || gitlens:views:scm:grouped:views:remotes)", "contextualTitle": "GitLens", "icon": "$(gitlens-remotes-view)", "visibility": "collapsed", "container": "scm", "order": 3}, "gitlens.views.repositories": {"name": "Repositories", "when": "!gitlens:disabled && !(config.gitlens.views.scm.grouped.views.repositories || gitlens:views:scm:grouped:views:repositories)", "contextualTitle": "GitLens", "icon": "$(gitlens-repositories-view)", "visibility": "hidden", "container": "scm", "order": 0}, "gitlens.views.scm.grouped": {"name": "GitLens", "when": "!gitlens:disabled && ((config.gitlens.views.scm.grouped.views.branches || gitlens:views:scm:grouped:views:branches) || (config.gitlens.views.scm.grouped.views.commits || gitlens:views:scm:grouped:views:commits) || (config.gitlens.views.scm.grouped.views.contributors || gitlens:views:scm:grouped:views:contributors) || (config.gitlens.views.scm.grouped.views.launchpad || gitlens:views:scm:grouped:views:launchpad) || (config.gitlens.views.scm.grouped.views.remotes || gitlens:views:scm:grouped:views:remotes) || (config.gitlens.views.scm.grouped.views.repositories || gitlens:views:scm:grouped:views:repositories) || (config.gitlens.views.scm.grouped.views.searchAndCompare || gitlens:views:scm:grouped:views:searchAndCompare) || (config.gitlens.views.scm.grouped.views.stashes || gitlens:views:scm:grouped:views:stashes) || (config.gitlens.views.scm.grouped.views.tags || gitlens:views:scm:grouped:views:tags) || (config.gitlens.views.scm.grouped.views.worktrees || gitlens:views:scm:grouped:views:worktrees))", "contextualTitle": "GitLens", "icon": "$(gitlens-gitlens)", "visibility": "visible", "container": "scm", "order": 8, "welcomeContent": [{"contents": "Loading...", "when": "gitlens:views:scm:grouped:loading"}, {"contents": "GitLens groups many related views—Commits, Branches, Stashes, etc—here for easier view management.\n\n[Continue](command:gitlens.views.scm.grouped.welcome.dismiss)\n\nUse the tabs above to navigate, or detach the views you want to keep separated. You can regroup them anytime using the 'x' in the view header.", "when": "!gitlens:views:scm:grouped:loading && gitlens:views:scm:grouped:welcome && gitlens:install:new"}, {"contents": "GitLens groups many related views—Commits, Branches, Stashes, etc—here for easier view management.\n\n[Continue](command:gitlens.views.scm.grouped.welcome.dismiss)\n\nPrefer them separate? [Restore views to previous locations](command:gitlens.views.scm.grouped.welcome.restore)\n\nUse the tabs above to navigate, or detach the views you want to keep separated. You can regroup them anytime using the 'x' in the view header.", "when": "!gitlens:views:scm:grouped:loading && gitlens:views:scm:grouped:welcome && !gitlens:install:new"}, {"contents": "[Launchpad](command:gitlens.views.launchpad.info \"Learn about Launchpad\") — organizes your pull requests into actionable groups to help you focus and keep your team unblocked.", "when": "!gitlens:views:scm:grouped:loading && gitlens:views:scm:grouped:view == launchpad"}, {"contents": "[Connect an Integration...](command:gitlens.showLaunchpad?%7B%22source%22%3A%22launchpad-view%22%7D)\n\nAllows Launchpad to organize your pull requests into actionable groups and keep your team unblocked.", "when": "!gitlens:views:scm:grouped:loading && gitlens:views:scm:grouped:view == launchpad && !gitlens:launchpad:connected"}, {"contents": "[Resend Verification Email](command:gitlens.plus.resendVerification?%7B%22source%22%3A%22launchpad-view%22%7D)\n\nYou must verify your email before you can continue or [recheck Status](command:gitlens.plus.validate?%7B%22source%22%3A%22launchpad-view%22%7D).", "when": "!gitlens:views:scm:grouped:loading && gitlens:views:scm:grouped:view == launchpad && gitlens:launchpad:connected && gitlens:plus:state == -1"}, {"contents": "[Try GitLens Pro](command:gitlens.plus.signUp?%7B%22source%22%3A%22launchpad-view%22%7D)\n\nGet 14 days of GitLens Pro for free — no credit card required. Or [sign in](command:gitlens.plus.login?%7B%22source%22%3A%22launchpad-view%22%7D).", "when": "!gitlens:views:scm:grouped:loading && gitlens:views:scm:grouped:view == launchpad && gitlens:launchpad:connected && gitlens:plus:state == 0"}, {"contents": "[Upgrade to Pro](command:gitlens.plus.upgrade?%7B%22source%22%3A%22launchpad-view%22%7D)", "when": "!gitlens:views:scm:grouped:loading && gitlens:views:scm:grouped:view == launchpad && gitlens:launchpad:connected && gitlens:plus:state == 4"}, {"contents": "Limited-time sale on GitLens Pro.", "when": "!gitlens:views:scm:grouped:loading && gitlens:views:scm:grouped:view == launchpad && gitlens:launchpad:connected && gitlens:plus:state == 4 && gitlens:promo && gitlens:promo != pro50"}, {"contents": "Save 50% on GitLens Pro.", "when": "!gitlens:views:scm:grouped:loading && gitlens:views:scm:grouped:view == launchpad && gitlens:launchpad:connected && gitlens:plus:state == 4 && gitlens:promo == pro50"}, {"contents": "Your Pro trial has ended. Please upgrade for full access to Launchpad and other Pro features.", "when": "!gitlens:views:scm:grouped:loading && gitlens:views:scm:grouped:view == launchpad && gitlens:launchpad:connected && gitlens:plus:state == 4"}, {"contents": "[Continue](command:gitlens.plus.reactivateProTrial?%7B%22source%22%3A%22launchpad-view%22%7D)\n\nReactivate your Pro trial and experience Launchpad and all the new Pro features — free for another 14 days!", "when": "!gitlens:views:scm:grouped:loading && gitlens:views:scm:grouped:view == launchpad && gitlens:launchpad:connected && gitlens:plus:state == 5"}, {"contents": "Search for commits by [message](command:gitlens.views.searchAndCompare.searchCommits?%7B%22search%22%3A%7B%22query%22%3A%22message%3A%22%7D%2C%22prefillOnly%22%3Atrue%7D), [author](command:gitlens.views.searchAndCompare.searchCommits?%7B%22search%22%3A%7B%22query%22%3A%22author%3A%22%7D%2C%22prefillOnly%22%3Atrue%7D), [SHA](command:gitlens.views.searchAndCompare.searchCommits?%7B%22search%22%3A%7B%22query%22%3A%22commit%3A%22%7D%2C%22prefillOnly%22%3Atrue%7D), [file](command:gitlens.views.searchAndCompare.searchCommits?%7B%22search%22%3A%7B%22query%22%3A%22file%3A%22%7D%2C%22prefillOnly%22%3Atrue%7D), or [changes](command:gitlens.views.searchAndCompare.searchCommits?%7B%22search%22%3A%7B%22query%22%3A%22change%3A%22%7D%2C%22prefillOnly%22%3Atrue%7D)\n\n[Search Commits...](command:gitlens.views.searchAndCompare.searchCommits)", "when": "!gitlens:views:scm:grouped:loading && gitlens:views:scm:grouped:view == searchAndCompare && !gitlens:hasVirtualFolders"}, {"contents": "Search for commits by [message](command:gitlens.views.searchAndCompare.searchCommits?%7B%22search%22%3A%7B%22query%22%3A%22message%3A%22%7D%2C%22prefillOnly%22%3Atrue%7D), [author](command:gitlens.views.searchAndCompare.searchCommits?%7B%22search%22%3A%7B%22query%22%3A%22author%3A%22%7D%2C%22prefillOnly%22%3Atrue%7D), or [SHA](command:gitlens.views.searchAndCompare.searchCommits?%7B%22search%22%3A%7B%22query%22%3A%22commit%3A%22%7D%2C%22prefillOnly%22%3Atrue%7D)\n\n[Search Commits...](command:gitlens.views.searchAndCompare.searchCommits)", "when": "!gitlens:views:scm:grouped:loading && gitlens:views:scm:grouped:view == searchAndCompare && gitlens:hasVirtualFolders"}, {"contents": "Compare a <branch, tag, or ref> with another <branch, tag, or ref>\n\n[Compare References...](command:gitlens.views.searchAndCompare.selectForCompare)", "when": "!gitlens:views:scm:grouped:loading && gitlens:views:scm:grouped:view == searchAndCompare && !gitlens:hasVirtualFolders"}, {"contents": "[Worktrees](https://help.gitkraken.com/gitlens/side-bar/#worktrees-view-pro) ᴾᴿᴼ — minimize context switching by allowing you to work on multiple branches simultaneously.", "when": "!gitlens:views:scm:grouped:loading && gitlens:views:scm:grouped:view == worktrees && !gitlens:hasVirtualFolders"}, {"contents": "[Create Worktree...](command:gitlens.views.createWorktree)", "when": "!gitlens:views:scm:grouped:loading && gitlens:views:scm:grouped:view == worktrees && !gitlens:plus:required && !gitlens:feature:unsupported:git:worktrees"}, {"contents": "[Resend Verification Email](command:gitlens.plus.resendVerification?%7B%22source%22%3A%22worktrees%22%7D)\n\nYou must verify your email before you can continue or [recheck Status](command:gitlens.plus.validate?%7B%22source%22%3A%22worktrees%22%7D).", "when": "!gitlens:views:scm:grouped:loading && gitlens:views:scm:grouped:view == worktrees && gitlens:plus:state == -1"}, {"contents": "[Try GitLens Pro](command:gitlens.plus.signUp?%7B%22source%22%3A%22worktrees%22%7D)\n\nGet 14 days of GitLens Pro for free — no credit card required. Or [sign in](command:gitlens.plus.login?%7B%22source%22%3A%22worktrees%22%7D).", "when": "!gitlens:views:scm:grouped:loading && gitlens:views:scm:grouped:view == worktrees && gitlens:plus:required && gitlens:plus:state == 0"}, {"contents": "[Upgrade to Pro](command:gitlens.plus.upgrade?%7B%22source%22%3A%22worktrees%22%7D)", "when": "!gitlens:views:scm:grouped:loading && gitlens:views:scm:grouped:view == worktrees && gitlens:plus:required && gitlens:plus:state == 4"}, {"contents": "Limited-time sale on GitLens Pro.", "when": "!gitlens:views:scm:grouped:loading && gitlens:views:scm:grouped:view == worktrees && gitlens:plus:required && gitlens:plus:state == 4 && gitlens:promo && gitlens:promo != pro50"}, {"contents": "Save 50% on GitLens Pro.", "when": "!gitlens:views:scm:grouped:loading && gitlens:views:scm:grouped:view == worktrees && gitlens:plus:required && gitlens:plus:state == 4 && gitlens:promo == pro50"}, {"contents": "Your Pro trial has ended. Please upgrade for full access to Worktrees and other Pro features.", "when": "!gitlens:views:scm:grouped:loading && gitlens:views:scm:grouped:view == worktrees && gitlens:plus:required && gitlens:plus:state == 4"}, {"contents": "[Continue](command:gitlens.plus.reactivateProTrial?%7B%22source%22%3A%22worktrees%22%7D)\n\nReactivate your Pro trial and experience Worktrees and all the new Pro features — free for another 14 days!", "when": "!gitlens:views:scm:grouped:loading && gitlens:views:scm:grouped:view == worktrees && gitlens:plus:required && gitlens:plus:state == 5"}, {"contents": "Use on privately-hosted repos require [GitLens Pro](https://help.gitkraken.com/gitlens/gitlens-community-vs-gitlens-pro/).", "when": "!gitlens:views:scm:grouped:loading && gitlens:views:scm:grouped:view == worktrees && gitlens:plus:state != 6"}, {"contents": "⚠ Worktrees are not supported by your version of Git. Please upgrade to a more recent version.", "when": "!gitlens:views:scm:grouped:loading && gitlens:views:scm:grouped:view == worktrees && !gitlens:plus:required && gitlens:feature:unsupported:git:worktrees"}]}, "gitlens.views.searchAndCompare": {"name": "Search & Compare", "when": "!gitlens:disabled && !(config.gitlens.views.scm.grouped.views.searchAndCompare || gitlens:views:scm:grouped:views:searchAndCompare)", "contextualTitle": "GitLens", "icon": "$(gitlens-search-view)", "initialSize": 2, "visibility": "visible", "container": "gitlensInspect", "order": 5, "welcomeContent": [{"contents": "Search for commits by [message](command:gitlens.views.searchAndCompare.searchCommits?%7B%22search%22%3A%7B%22query%22%3A%22message%3A%22%7D%2C%22prefillOnly%22%3Atrue%7D), [author](command:gitlens.views.searchAndCompare.searchCommits?%7B%22search%22%3A%7B%22query%22%3A%22author%3A%22%7D%2C%22prefillOnly%22%3Atrue%7D), [SHA](command:gitlens.views.searchAndCompare.searchCommits?%7B%22search%22%3A%7B%22query%22%3A%22commit%3A%22%7D%2C%22prefillOnly%22%3Atrue%7D), [file](command:gitlens.views.searchAndCompare.searchCommits?%7B%22search%22%3A%7B%22query%22%3A%22file%3A%22%7D%2C%22prefillOnly%22%3Atrue%7D), or [changes](command:gitlens.views.searchAndCompare.searchCommits?%7B%22search%22%3A%7B%22query%22%3A%22change%3A%22%7D%2C%22prefillOnly%22%3Atrue%7D)\n\n[Search Commits...](command:gitlens.views.searchAndCompare.searchCommits)", "when": "!gitlens:hasVirtualFolders"}, {"contents": "Search for commits by [message](command:gitlens.views.searchAndCompare.searchCommits?%7B%22search%22%3A%7B%22query%22%3A%22message%3A%22%7D%2C%22prefillOnly%22%3Atrue%7D), [author](command:gitlens.views.searchAndCompare.searchCommits?%7B%22search%22%3A%7B%22query%22%3A%22author%3A%22%7D%2C%22prefillOnly%22%3Atrue%7D), or [SHA](command:gitlens.views.searchAndCompare.searchCommits?%7B%22search%22%3A%7B%22query%22%3A%22commit%3A%22%7D%2C%22prefillOnly%22%3Atrue%7D)\n\n[Search Commits...](command:gitlens.views.searchAndCompare.searchCommits)", "when": "gitlens:hasVirtualFolders"}, {"contents": "Compare a <branch, tag, or ref> with another <branch, tag, or ref>\n\n[Compare References...](command:gitlens.views.searchAndCompare.selectForCompare)", "when": "!gitlens:hasVirtualFolders"}]}, "gitlens.views.stashes": {"name": "Stashes", "when": "!gitlens:disabled && !gitlens:hasVirtualFolders && !(config.gitlens.views.scm.grouped.views.stashes || gitlens:views:scm:grouped:views:stashes)", "contextualTitle": "GitLens", "icon": "$(gitlens-stashes-view)", "visibility": "collapsed", "container": "scm", "order": 4}, "gitlens.views.tags": {"name": "Tags", "when": "!gitlens:disabled && !(config.gitlens.views.scm.grouped.views.tags || gitlens:views:scm:grouped:views:tags)", "contextualTitle": "GitLens", "icon": "$(gitlens-tags-view)", "visibility": "collapsed", "container": "scm", "order": 5}, "gitlens.views.timeline": {"type": "webview", "name": "Visual File History", "when": "!gitlens:disabled && !gitlens:plus:disabled", "contextualTitle": "GitLens", "icon": "$(graph-scatter)", "initialSize": 1, "visibility": "visible", "container": "gitlensInspect", "order": 4}, "gitlens.views.workspaces": {"name": "Cloud Workspaces", "when": "!gitlens:untrusted && !gitlens:hasVirtualFolders", "contextualTitle": "GitLens", "icon": "$(gitlens-workspaces-view)", "initialSize": 2, "visibility": "collapsed", "container": "gitlens", "order": 3, "welcomeContent": [{"contents": "Workspaces ᴘʀᴇᴠɪᴇᴡ — group and manage multiple repositories together, accessible from anywhere, streamlining your workflow.\n\nCreate workspaces just for yourself or share (coming soon in GitLens) them with your team for faster onboarding and better collaboration."}, {"contents": "[Create Cloud Workspace](command:gitlens.views.workspaces.create)", "when": "gitlens:plus"}, {"contents": "[Try GitLens Pro](command:gitlens.plus.signUp?%7B%22source%22%3A%22workspaces%22%7D)\n\nGet 14 days of GitLens Pro for free — no credit card required. Or [sign in](command:gitlens.plus.login?%7B%22source%22%3A%22workspaces%22%7D).", "when": "!gitlens:plus"}, {"contents": "An account is required and may require [GitLens Pro](https://help.gitkraken.com/gitlens/gitlens-community-vs-gitlens-pro/) in the future.", "when": "gitlens:plus:state != 6"}]}, "gitlens.views.worktrees": {"name": "Worktrees", "when": "!gitlens:disabled && !gitlens:plus:disabled && !gitlens:hasVirtualFolders && !(config.gitlens.views.scm.grouped.views.worktrees || gitlens:views:scm:grouped:views:worktrees)", "contextualTitle": "GitLens", "icon": "$(gitlens-worktrees-view)", "visibility": "collapsed", "container": "scm", "order": 6, "welcomeContent": [{"contents": "[Worktrees](https://help.gitkraken.com/gitlens/side-bar/#worktrees-view-pro) ᴾᴿᴼ — minimize context switching by working on multiple branches simultaneously."}, {"contents": "[Create Worktree...](command:gitlens.views.createWorktree)", "when": "!gitlens:plus:required && !gitlens:feature:unsupported:git:worktrees"}, {"contents": "[Resend Verification Email](command:gitlens.plus.resendVerification?%7B%22source%22%3A%22worktrees%22%7D)\n\nYou must verify your email before you can continue or [recheck Status](command:gitlens.plus.validate?%7B%22source%22%3A%22worktrees%22%7D).", "when": "gitlens:plus:state == -1"}, {"contents": "[Try GitLens Pro](command:gitlens.plus.signUp?%7B%22source%22%3A%22worktrees%22%7D)\n\nGet 14 days of GitLens Pro for free — no credit card required. Or [sign in](command:gitlens.plus.login?%7B%22source%22%3A%22worktrees%22%7D).", "when": "gitlens:plus:required && gitlens:plus:state == 0"}, {"contents": "[Upgrade to Pro](command:gitlens.plus.upgrade?%7B%22source%22%3A%22worktrees%22%7D)", "when": "gitlens:plus:required && gitlens:plus:state == 4"}, {"contents": "Limited-time sale on GitLens Pro.", "when": "gitlens:plus:required && gitlens:plus:state == 4 && gitlens:promo && gitlens:promo != pro50"}, {"contents": "Save 50% on GitLens Pro.", "when": "gitlens:plus:required && gitlens:plus:state == 4 && gitlens:promo == pro50"}, {"contents": "Your Pro trial has ended. Please upgrade for full access to Worktrees and other Pro features.", "when": "gitlens:plus:required && gitlens:plus:state == 4"}, {"contents": "[Continue](command:gitlens.plus.reactivateProTrial?%7B%22source%22%3A%22worktrees%22%7D)\n\nReactivate your Pro trial and experience Worktrees and all the new Pro features — free for another 14 days!", "when": "gitlens:plus:required && gitlens:plus:state == 5"}, {"contents": "Use on privately-hosted repos require [GitLens Pro](https://help.gitkraken.com/gitlens/gitlens-community-vs-gitlens-pro/).", "when": "gitlens:plus:state != 6"}, {"contents": "⚠ Worktrees are not supported by your version of Git. Please upgrade to a more recent version.", "when": "!gitlens:plus:required && gitlens:feature:unsupported:git:worktrees"}]}}}